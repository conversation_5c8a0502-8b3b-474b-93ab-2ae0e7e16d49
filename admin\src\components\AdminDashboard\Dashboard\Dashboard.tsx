"use client";

import { useEffect } from "react";
import AdminService, { UserStatistics } from "../../../services/admin.services";
import UserManagementService, {
  User,
} from "../../../services/usermanagement.services";
import { BsReverseLayoutSidebarReverse } from "react-icons/bs";
import { FaGripVertical } from "react-icons/fa";
import { FiUsers } from "react-icons/fi";
import { GoArrowDownRight } from "react-icons/go";
import { IoDocumentTextOutline } from "react-icons/io5";
import { MdArrowOutward } from "react-icons/md";
import {
  Area,
  AreaChart,
  Bar,
  BarChart,
  CartesianGrid,
  Cell,
  Pie,
  PieChart,
  ResponsiveContainer,
  Tooltip,
  XAxis,
  YAxis,
} from "recharts";
import { useImmerReducer } from "use-immer";
import DashboardSkeleton from "../../Skeletons/DashboardSkeleton";

type NewUserData = {
  day: string;
  users: number;
  originalDate?: Date;
};

type UserApprovalData = {
  name: string;
  value: number;
  color: string;
};

type BusinessListingData = {
  week: string;
  Active: number;
  "Waiting Approval": number;
};

type RecentActivityData = {
  id: number;
  text: string;
  time: string;
  color: string;
};

type DashboardState = {
  newUsersData: NewUserData[];
  userApprovalData: UserApprovalData[];
  businessListingsData: BusinessListingData[];
  recentActivity: RecentActivityData[];
  isLoading: boolean;
  error: string | null;
  userStats: UserStatistics | null;
  users: User[];
  activeCount: number;
  pendingCount: number;
};

type DashboardAction =
  | { type: "SET_NEW_USERS_DATA"; payload: NewUserData[] }
  | { type: "SET_USER_APPROVAL_DATA"; payload: UserApprovalData[] }
  | { type: "SET_BUSINESS_LISTINGS_DATA"; payload: BusinessListingData[] }
  | { type: "SET_RECENT_ACTIVITY"; payload: RecentActivityData[] }
  | { type: "SET_LOADING"; payload: boolean }
  | { type: "SET_ERROR"; payload: string | null }
  | { type: "SET_USER_STATS"; payload: UserStatistics | null }
  | { type: "SET_USERS"; payload: User[] }
  | { type: "UPDATE_USER_COUNTS" }
  | {
      type: "SET_USER_STATS"; 
      payload: UserStatistics | null;
    }
  | {
      type: "SET_ALL_DATA";
      payload: {
        newUsers: NewUserData[];
        userApproval: UserApprovalData[];
        businessListings: BusinessListingData[];
        recentActivity: RecentActivityData[];
      };
    };

const initialState: DashboardState = {
  newUsersData: [],
  userApprovalData: [],
  businessListingsData: [],
  recentActivity: [],
  isLoading: true,
  error: null,
  userStats: null,
  users: [],
  activeCount: 0,
  pendingCount: 0,
};

function dashboardReducer(draft: DashboardState, action: DashboardAction) {
  switch (action.type) {
    case "SET_NEW_USERS_DATA":
      draft.newUsersData = action.payload;
      break;
    case "SET_USER_APPROVAL_DATA":
      draft.userApprovalData = action.payload;
      break;
    case "SET_BUSINESS_LISTINGS_DATA":
      draft.businessListingsData = action.payload;
      break;
    case "SET_RECENT_ACTIVITY":
      draft.recentActivity = action.payload;
      break;
    case "SET_LOADING":
      draft.isLoading = action.payload;
      break;
    case "SET_ERROR":
      draft.error = action.payload;
      break;
    case "SET_USER_STATS":
      draft.userStats = action.payload;
      break;
    case "SET_USERS":
      draft.users = action.payload;
      break;
    case "UPDATE_USER_COUNTS":
      // Count active and pending users
      const activeUsers = draft.users.filter(
        (user) => user.status === "ACTIVE"
      ).length;
      const pendingUsers = draft.users.filter(
        (user) => user.status === "PENDING"
      ).length;

      draft.activeCount = activeUsers;
      draft.pendingCount = pendingUsers;

      // Update the userApprovalData for the pie chart
      draft.userApprovalData = [
        { name: "Active", value: activeUsers, color: "#4F46E5" },
        { name: "Waiting Approval", value: pendingUsers, color: "#60A5FA" },
      ];
      break;
    case "SET_ALL_DATA":
      draft.newUsersData = action.payload.newUsers;
      draft.businessListingsData = action.payload.businessListings;
      draft.recentActivity = action.payload.recentActivity;
      // Note: userApprovalData is now calculated from real user data, not from the payload
      break;
  }
}

export default function Dashboard() {
  const [state, dispatch] = useImmerReducer(dashboardReducer, initialState);
  const {
    newUsersData,
    userApprovalData,
    businessListingsData,
    recentActivity,
    isLoading,
    userStats,
    users,
  } = state;

  useEffect(() => {
    const fetchDashboardData = async () => {
      try {
        dispatch({ type: "SET_LOADING", payload: true });

        // Fetch real users data from API
        const users = await UserManagementService.getUsers();
        console.log('Fetched users:', users); // Debug log to see user structure
        dispatch({ type: "SET_USERS", payload: users });

        // Calculate user statistics from actual users data
        const userStatistics = {
          totalUsers: users.length,
          percentageChange: 0, // This would need to be calculated if you have historical data
          last7DaysNewUsers: [], // We'll calculate this from users array
        };

        // Initialize counts for each day of the week
        const dayOfWeekCounts = [0, 0, 0, 0, 0, 0, 0]; // Sun=0, Mon=1, ..., Sat=6
        const dayNames = ["Sun", "Mon", "Tue", "Wed", "Thu", "Fri", "Sat"];
        
        // Count users by day of week they joined
        // If joinDate is missing, distribute evenly
        let usersWithoutJoinDate = 0;
        
        users.forEach((user) => {
          if (user.joinDate) {
            try {
              const joinDate = new Date(user.joinDate);
              const dayOfWeek = joinDate.getDay(); // 0-6
              dayOfWeekCounts[dayOfWeek]++;
            } catch (e) {
              // If date parsing fails, count as user without join date
              usersWithoutJoinDate++;
            }
          } else {
            usersWithoutJoinDate++;
          }
        });
        
        // Distribute users without join date evenly
        if (usersWithoutJoinDate > 0) {
          const basePerDay = Math.floor(usersWithoutJoinDate / 7);
          let remaining = usersWithoutJoinDate % 7;
          
          for (let i = 0; i < 7; i++) {
            dayOfWeekCounts[i] += basePerDay;
            if (remaining > 0) {
              dayOfWeekCounts[i]++;
              remaining--;
            }
          }
        }
        
        // Create chart data - reorder to start with Monday
        const chartData = [];
        
        // Monday (1) to Saturday (6)
        for (let i = 1; i <= 6; i++) {
          chartData.push({
            day: dayNames[i],
            users: dayOfWeekCounts[i],
            originalDate: new Date(),
          });
        }
        
        // Add Sunday (0) at the end
        chartData.push({
          day: dayNames[0],
          users: dayOfWeekCounts[0],
          originalDate: new Date(),
        });
        
        console.log('Users count:', users.length);
        console.log('Chart data:', chartData);

        // Use the chartData directly as our newUsersData
        const newUsersData = chartData;

        // Update state with calculated data
        dispatch({ type: "SET_NEW_USERS_DATA", payload: newUsersData });
        dispatch({ type: "SET_USER_STATS", payload: userStatistics });
        dispatch({ type: "UPDATE_USER_COUNTS" });

        const [businessListingsResponse, recentActivityResponse] =
          await Promise.all([
            fetch("/data/businessListingsData.json"),
            fetch("/data/recentActivityData.json"),
          ]);

        const businessListings = await businessListingsResponse.json();
        const recentActivity = await recentActivityResponse.json();

        dispatch({
          type: "SET_ALL_DATA",
          payload: {
            newUsers: newUsersData,
            userApproval: [], // Empty as we're using real data now
            businessListings,
            recentActivity,
          },
        });
      } catch (error) {
        console.error("Error fetching dashboard data:", error);
        dispatch({
          type: "SET_ERROR",
          payload: "Failed to load dashboard data",
        });
      } finally {
        dispatch({ type: "SET_LOADING", payload: false });
      }
    };

    fetchDashboardData();
  }, [dispatch]);

  if (isLoading) {
    return <DashboardSkeleton />;
  }

  return (
    <div className="mt-4 ">
      {/* Metric Cards */}
      <div className="grid grid-cols-1 gap-6 md:grid-cols-2 lg:grid-cols-4">
        {/* Total Users Card */}
        <div className="rounded-lg border border-gray-200 p-2 px-4 bg-white shadow-sm">
          <div className="flex justify-between">
            <div className="text-sm font-medium text-gray-500">Total Users</div>
            <div className="text-primary bg-[#F3F4F6] p-2 rounded-md">
              <FiUsers className="font-bold text-xl text-[#4361EE]" />
            </div>
          </div>
          <div className="text-2xl font-bold">
            {users.length.toLocaleString()}
          </div>
          <div className="my-1 text-xs text-gray-500">
            Total registered users on platform
          </div>
          <div
            className={`flex items-center text-xs ${
              userStats && userStats.percentageChange >= 0
                ? "text-green-500"
                : "text-red-500"
            }`}
          >
            {userStats && userStats.percentageChange >= 0 ? (
              <MdArrowOutward />
            ) : (
              <GoArrowDownRight />
            )}
            {userStats ? Math.abs(userStats.percentageChange) : 0}%{" "}
            {userStats && userStats.percentageChange >= 0
              ? "increase"
              : "decrease"}
          </div>
        </div>

        {/* Active Listings Card */}
        <div className="rounded-lg border border-gray-200 p-2 px-4 bg-white shadow-sm">
          <div className="flex justify-between">
            <div className="text-sm font-medium text-gray-500">
              Active Listings
            </div>
            <div className="text-primary bg-[#F3F4F6] p-2 rounded-md">
              <FaGripVertical className="font-bold text-xl text-[#4361EE]" />
            </div>
          </div>
          <div className="text-2xl font-bold">149</div>
          <div className="my-1 text-xs text-gray-500">
            Business listings currently active
          </div>
          <div className="flex items-center text-xs text-green-500">
            <MdArrowOutward />
            8% increase
          </div>
        </div>

        {/* Pending Posts Card */}
        <div className="rounded-lg border border-gray-200 p-2 px-4 bg-white shadow-sm">
          <div className="flex justify-between">
            <div className="text-sm font-medium text-gray-500">
              Pending Posts
            </div>
            <div className="text-primary bg-[#F3F4F6] p-2 rounded-md">
              <IoDocumentTextOutline className="font-bold text-xl text-[#4361EE]" />
            </div>
          </div>
          <div className="text-2xl font-bold">24</div>
          <div className="my-1 text-xs text-gray-500">
            Consultant posts awaiting approval
          </div>
          <div className="flex items-center text-xs text-red-500">
            <GoArrowDownRight />
            3% decrease
          </div>
        </div>

        {/* Revenue Card */}
        <div className="rounded-lg border border-gray-200 p-2 px-4 bg-white shadow-sm">
          <div className="flex justify-between">
            <div className="text-sm font-medium text-gray-500">Revenue</div>
            <div className="text-primary bg-[#F3F4F6] p-2 rounded-md">
              <BsReverseLayoutSidebarReverse className="transform rotate-270 font-bold text-xl text-[#4361EE]" />
            </div>
          </div>
          <div className="text-2xl font-bold">$28,459</div>
          <div className="my-1 text-xs text-gray-500">
            Monthly subscription revenue
          </div>
          <div className="flex items-center text-xs text-green-500">
            <MdArrowOutward />
            13% increase
          </div>
        </div>
      </div>

      {/* Dashboard Content (formerly Overview Tab) */}
      <div className="mt-3">
        <div className="flex items-stretch gap-5 mb-3 flex-col lg:flex-row">
          {/* New Users Chart */}
          <div className="rounded-lg border border-gray-200 bg-white py-2 px-4 shadow-sm lg:w-4/6 md:h-[270px] lg:h-[380px]">
            <h3 className="text-lg font-bold">New Users (Last 7 days)</h3>
            <p className="text-xs text-gray-500">
              Number of new user registrations
            </p>
            <div className="mt-2 md:h-[200px] lg:h-[300px]">
              <ResponsiveContainer width="100%" height="100%">
                <AreaChart
                  data={newUsersData}
                  margin={{ top: 10, right: 10, left: 0, bottom: 0 }}
                >
                  <defs>
                    <linearGradient id="colorUsers" x1="0" y1="0" x2="0" y2="1">
                      <stop offset="5%" stopColor="#4F46E5" stopOpacity={0.3} />
                      <stop
                        offset="95%"
                        stopColor="#4F46E5"
                        stopOpacity={0.1}
                      />
                    </linearGradient>
                  </defs>
                  <CartesianGrid
                    strokeDasharray="3 3"
                    stroke="#f0f0f0"
                    vertical={false}
                  />
                  <XAxis
                    dataKey="day"
                    axisLine={false}
                    tickLine={false}
                    tick={{ fontSize: 12, fill: "#6B7280" }}
                  />
                  <YAxis
                    axisLine={false}
                    tickLine={false}
                    tick={{ fontSize: 12, fill: "#6B7280" }}
                    domain={[0, "auto"]}
                    allowDecimals={false}
                  />
                  <Tooltip />
                  <Area
                    type="monotone"
                    dataKey="users"
                    stroke="#4F46E5"
                    strokeWidth={2}
                    fillOpacity={1}
                    fill="url(#colorUsers)"
                  />
                </AreaChart>
              </ResponsiveContainer>
            </div>
          </div>

          {/* Engagement Distribution */}
          <div className="rounded-lg border border-gray-200 bg-white py-2 px-4 shadow-sm lg:w-2/6 md:h-[270px] lg:h-[380px]">
            <h3 className="text-lg font-bold">User Approval Status</h3>
            <p className="text-xs text-gray-500">
              Breakdown of Active and Pending Users
            </p>
            <div className="flex md:h-[190px] lg:h-[270px] items-center justify-center">
              <ResponsiveContainer width="100%" height="100%">
                <PieChart>
                  <Pie
                    data={userApprovalData}
                    cx="50%"
                    cy="50%"
                    innerRadius={50}
                    outerRadius={75}
                    paddingAngle={0}
                    dataKey="value"
                  >
                    {userApprovalData.map((entry, index) => (
                      <Cell key={`cell-${index}`} fill={entry.color} />
                    ))}
                  </Pie>
                  <Tooltip />
                </PieChart>
              </ResponsiveContainer>
            </div>
            <div className="flex space-x-8">
              {userApprovalData.map((entry, index) => (
                <div key={index} className="flex items-center">
                  <div
                    className="mr-2 h-3 w-3"
                    style={{ backgroundColor: entry.color }}
                  ></div>
                  <span className="text-sm">{entry.name}</span>
                  <span className="ml-1 text-sm font-medium">
                    {entry.value}
                  </span>
                </div>
              ))}
            </div>
          </div>
        </div>

        <div className="flex items-stretch gap-5 flex-col lg:flex-row">
          {/* Business Listings */}
          <div className="rounded-lg border border-gray-200 bg-white px-4 py-2 shadow-sm lg:w-7/12 md:h-[270px] lg:h-[380px]">
            <h3 className="text-lg font-bold">Business Listings</h3>
            <p className="text-xs text-gray-500">
              Active vs pending listings by week
            </p>
            <div className="mt-2 md:h-[200px] lg:h-[300px]">
              <ResponsiveContainer width="100%" height="100%">
                <BarChart
                  data={businessListingsData}
                  margin={{ top: 10, right: 10, left: 0, bottom: 20 }}
                >
                  <CartesianGrid
                    strokeDasharray="3 3"
                    stroke="#f0f0f0"
                    vertical={false}
                  />
                  <XAxis
                    dataKey="week"
                    axisLine={false}
                    tickLine={false}
                    tick={{ fontSize: 12, fill: "#6B7280" }}
                  />
                  <YAxis
                    axisLine={false}
                    tickLine={false}
                    tick={{ fontSize: 12, fill: "#6B7280" }}
                    domain={[0, "dataMax + 10"]}
                    ticks={[0, 10, 20, 30, 40]}
                  />
                  <Tooltip />
                  <Bar dataKey="Active" fill="#4F46E5" barSize={20} />
                  <Bar dataKey="Waiting Approval" fill="#60A5FA" barSize={20} />
                </BarChart>
              </ResponsiveContainer>
            </div>
            <div className="flex justify-start space-x-8 -mt-4">
              <div className="flex items-center">
                <div className="mr-2 h-3 w-3 bg-[#4F46E5]"></div>
                <span className="text-sm">Active</span>
              </div>
              <div className="flex items-center">
                <div className="mr-2 h-3 w-3 bg-[#60A5FA]"></div>
                <span className="text-sm">Waiting Approval</span>
              </div>
            </div>
          </div>

          {/* Recent Activity */}
          <div className="rounded-lg border border-gray-200 bg-white px-4 py-2 shadow-sm lg:w-5/12 md:h-[270px] lg:h-[380px]">
            <h3 className="text-lg font-bold">Recent Activity</h3>
            <p className="text-xs text-gray-500">Latest system activity</p>
            <div className="mt-2 md:h-[200px] lg:h-[300px]">
              <div className="mb-4 text-sm font-medium">Today</div>
              <div className="space-y-4">
                {recentActivity.map((activity) => (
                  <div key={activity.id} className="flex items-center">
                    <div
                      className="mr-4 flex h-6 w-6 items-center justify-center rounded-full"
                      style={{ backgroundColor: `${activity.color}20` }}
                    >
                      <div
                        className="h-2 w-2 rounded-full"
                        style={{ backgroundColor: activity.color }}
                      ></div>
                    </div>
                    <div className="flex justify-between items-center w-full">
                      <h1 className="text-sm">{activity.text}</h1>
                      <p className="text-xs text-gray-500">{activity.time}</p>
                    </div>
                  </div>
                ))}
              </div>
            </div>
          </div>
        </div>
      </div>
    </div>
  );
}
