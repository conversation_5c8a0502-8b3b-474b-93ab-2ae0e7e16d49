
/* !!! This is code generated by Prisma. Do not edit directly. !!!
/* eslint-disable */

Object.defineProperty(exports, "__esModule", { value: true });

const {
  Decimal,
  objectEnumValues,
  makeStrictEnum,
  Public,
  getRuntime,
  skip
} = require('@prisma/client/runtime/index-browser.js')


const Prisma = {}

exports.Prisma = Prisma
exports.$Enums = {}

/**
 * Prisma Client JS version: 6.8.2
 * Query Engine version: 2060c79ba17c6bb9f5823312b6f6b7f4a845738e
 */
Prisma.prismaVersion = {
  client: "6.8.2",
  engine: "2060c79ba17c6bb9f5823312b6f6b7f4a845738e"
}

Prisma.PrismaClientKnownRequestError = () => {
  const runtimeName = getRuntime().prettyName;
  throw new Error(`PrismaClientKnownRequestError is unable to run in this browser environment, or has been bundled for the browser (running in ${runtimeName}).
In case this error is unexpected for you, please report it in https://pris.ly/prisma-prisma-bug-report`,
)};
Prisma.PrismaClientUnknownRequestError = () => {
  const runtimeName = getRuntime().prettyName;
  throw new Error(`PrismaClientUnknownRequestError is unable to run in this browser environment, or has been bundled for the browser (running in ${runtimeName}).
In case this error is unexpected for you, please report it in https://pris.ly/prisma-prisma-bug-report`,
)}
Prisma.PrismaClientRustPanicError = () => {
  const runtimeName = getRuntime().prettyName;
  throw new Error(`PrismaClientRustPanicError is unable to run in this browser environment, or has been bundled for the browser (running in ${runtimeName}).
In case this error is unexpected for you, please report it in https://pris.ly/prisma-prisma-bug-report`,
)}
Prisma.PrismaClientInitializationError = () => {
  const runtimeName = getRuntime().prettyName;
  throw new Error(`PrismaClientInitializationError is unable to run in this browser environment, or has been bundled for the browser (running in ${runtimeName}).
In case this error is unexpected for you, please report it in https://pris.ly/prisma-prisma-bug-report`,
)}
Prisma.PrismaClientValidationError = () => {
  const runtimeName = getRuntime().prettyName;
  throw new Error(`PrismaClientValidationError is unable to run in this browser environment, or has been bundled for the browser (running in ${runtimeName}).
In case this error is unexpected for you, please report it in https://pris.ly/prisma-prisma-bug-report`,
)}
Prisma.Decimal = Decimal

/**
 * Re-export of sql-template-tag
 */
Prisma.sql = () => {
  const runtimeName = getRuntime().prettyName;
  throw new Error(`sqltag is unable to run in this browser environment, or has been bundled for the browser (running in ${runtimeName}).
In case this error is unexpected for you, please report it in https://pris.ly/prisma-prisma-bug-report`,
)}
Prisma.empty = () => {
  const runtimeName = getRuntime().prettyName;
  throw new Error(`empty is unable to run in this browser environment, or has been bundled for the browser (running in ${runtimeName}).
In case this error is unexpected for you, please report it in https://pris.ly/prisma-prisma-bug-report`,
)}
Prisma.join = () => {
  const runtimeName = getRuntime().prettyName;
  throw new Error(`join is unable to run in this browser environment, or has been bundled for the browser (running in ${runtimeName}).
In case this error is unexpected for you, please report it in https://pris.ly/prisma-prisma-bug-report`,
)}
Prisma.raw = () => {
  const runtimeName = getRuntime().prettyName;
  throw new Error(`raw is unable to run in this browser environment, or has been bundled for the browser (running in ${runtimeName}).
In case this error is unexpected for you, please report it in https://pris.ly/prisma-prisma-bug-report`,
)}
Prisma.validator = Public.validator

/**
* Extensions
*/
Prisma.getExtensionContext = () => {
  const runtimeName = getRuntime().prettyName;
  throw new Error(`Extensions.getExtensionContext is unable to run in this browser environment, or has been bundled for the browser (running in ${runtimeName}).
In case this error is unexpected for you, please report it in https://pris.ly/prisma-prisma-bug-report`,
)}
Prisma.defineExtension = () => {
  const runtimeName = getRuntime().prettyName;
  throw new Error(`Extensions.defineExtension is unable to run in this browser environment, or has been bundled for the browser (running in ${runtimeName}).
In case this error is unexpected for you, please report it in https://pris.ly/prisma-prisma-bug-report`,
)}

/**
 * Shorthand utilities for JSON filtering
 */
Prisma.DbNull = objectEnumValues.instances.DbNull
Prisma.JsonNull = objectEnumValues.instances.JsonNull
Prisma.AnyNull = objectEnumValues.instances.AnyNull

Prisma.NullTypes = {
  DbNull: objectEnumValues.classes.DbNull,
  JsonNull: objectEnumValues.classes.JsonNull,
  AnyNull: objectEnumValues.classes.AnyNull
}



/**
 * Enums
 */

exports.Prisma.TransactionIsolationLevel = makeStrictEnum({
  ReadUncommitted: 'ReadUncommitted',
  ReadCommitted: 'ReadCommitted',
  RepeatableRead: 'RepeatableRead',
  Serializable: 'Serializable'
});

exports.Prisma.UserScalarFieldEnum = {
  id: 'id',
  name: 'name',
  email: 'email',
  phoneNumber: 'phoneNumber',
  password: 'password',
  status: 'status',
  createdAt: 'createdAt',
  updatedAt: 'updatedAt',
  isDeleted: 'isDeleted'
};

exports.Prisma.UserProfileScalarFieldEnum = {
  id: 'id',
  userId: 'userId',
  imageUrl: 'imageUrl',
  professionalHeadline: 'professionalHeadline',
  industrySpecialization: 'industrySpecialization',
  areasOfExpertise: 'areasOfExpertise',
  portfolioLink: 'portfolioLink',
  introduction: 'introduction',
  rating: 'rating',
  hourlyRate: 'hourlyRate',
  createdAt: 'createdAt',
  updatedAt: 'updatedAt'
};

exports.Prisma.SubscriptionScalarFieldEnum = {
  id: 'id',
  userId: 'userId',
  planType: 'planType',
  startDate: 'startDate',
  endDate: 'endDate',
  price: 'price',
  period: 'period',
  description: 'description',
  features: 'features',
  createdAt: 'createdAt',
  updatedAt: 'updatedAt'
};

exports.Prisma.UserRoleAssignmentScalarFieldEnum = {
  id: 'id',
  userId: 'userId',
  role: 'role',
  createdAt: 'createdAt'
};

exports.Prisma.NotificationSettingsScalarFieldEnum = {
  id: 'id',
  userId: 'userId',
  newMessages: 'newMessages',
  newListings: 'newListings',
  investorInterest: 'investorInterest',
  appUpdates: 'appUpdates',
  marketingEmails: 'marketingEmails'
};

exports.Prisma.DocumentScalarFieldEnum = {
  id: 'id',
  userProfileId: 'userProfileId',
  companyId: 'companyId',
  documentUrl: 'documentUrl',
  documentType: 'documentType',
  metadata: 'metadata',
  createdAt: 'createdAt',
  updatedAt: 'updatedAt'
};

exports.Prisma.CompanyScalarFieldEnum = {
  id: 'id',
  userId: 'userId',
  name: 'name',
  logo: 'logo',
  industry: 'industry',
  location: 'location',
  size: 'size',
  revenue: 'revenue',
  status: 'status',
  website: 'website',
  description: 'description',
  established: 'established',
  employees: 'employees',
  annualRevenue: 'annualRevenue',
  equityOffered: 'equityOffered',
  revenueGrowth: 'revenueGrowth',
  ebitda: 'ebitda',
  ebitdaGrowth: 'ebitdaGrowth',
  profitMargin: 'profitMargin',
  profitMarginGrowth: 'profitMarginGrowth',
  businessType: 'businessType',
  createdAt: 'createdAt',
  updatedAt: 'updatedAt'
};

exports.Prisma.ListingScalarFieldEnum = {
  id: 'id',
  title: 'title',
  image: 'image',
  askingPrice: 'askingPrice',
  category: 'category',
  status: 'status',
  location: 'location',
  description: 'description',
  isFavorite: 'isFavorite',
  ownerId: 'ownerId',
  companyId: 'companyId',
  createdAt: 'createdAt',
  updatedAt: 'updatedAt'
};

exports.Prisma.ListingInterestScalarFieldEnum = {
  id: 'id',
  listingId: 'listingId',
  userId: 'userId',
  status: 'status',
  message: 'message',
  createdAt: 'createdAt',
  updatedAt: 'updatedAt'
};

exports.Prisma.ListingFinancialMetricScalarFieldEnum = {
  id: 'id',
  listingId: 'listingId',
  ebitda: 'ebitda',
  revenueYoYChange: 'revenueYoYChange',
  ebitdaYoYChange: 'ebitdaYoYChange',
  profitMarginYoYChange: 'profitMarginYoYChange',
  createdAt: 'createdAt',
  updatedAt: 'updatedAt'
};

exports.Prisma.MonthlyFinancialScalarFieldEnum = {
  id: 'id',
  listingId: 'listingId',
  month: 'month',
  year: 'year',
  revenue: 'revenue',
  ebitda: 'ebitda',
  profitMargin: 'profitMargin',
  createdAt: 'createdAt',
  updatedAt: 'updatedAt'
};

exports.Prisma.AdminScalarFieldEnum = {
  id: 'id',
  name: 'name',
  email: 'email',
  password: 'password',
  image: 'image',
  phone: 'phone',
  country: 'country',
  address: 'address',
  location: 'location',
  company: 'company',
  website: 'website',
  bio: 'bio',
  createdAt: 'createdAt',
  updatedAt: 'updatedAt',
  isDeleted: 'isDeleted',
  role: 'role'
};

exports.Prisma.PasswordResetScalarFieldEnum = {
  id: 'id',
  email: 'email',
  otp: 'otp',
  expiresAt: 'expiresAt',
  createdAt: 'createdAt'
};

exports.Prisma.TempUserScalarFieldEnum = {
  id: 'id',
  name: 'name',
  email: 'email',
  phone: 'phone',
  password: 'password',
  createdAt: 'createdAt',
  isDeleted: 'isDeleted',
  status: 'status',
  updatedAt: 'updatedAt',
  roles: 'roles'
};

exports.Prisma.SortOrder = {
  asc: 'asc',
  desc: 'desc'
};

exports.Prisma.NullableJsonNullValueInput = {
  DbNull: Prisma.DbNull,
  JsonNull: Prisma.JsonNull
};

exports.Prisma.QueryMode = {
  default: 'default',
  insensitive: 'insensitive'
};

exports.Prisma.NullsOrder = {
  first: 'first',
  last: 'last'
};

exports.Prisma.JsonNullValueFilter = {
  DbNull: Prisma.DbNull,
  JsonNull: Prisma.JsonNull,
  AnyNull: Prisma.AnyNull
};
exports.UserStatus = exports.$Enums.UserStatus = {
  ACTIVE: 'ACTIVE',
  REJECTED: 'REJECTED',
  PENDING: 'PENDING'
};

exports.UserRole = exports.$Enums.UserRole = {
  BUSINESS_OWNER: 'BUSINESS_OWNER',
  INVESTOR: 'INVESTOR',
  CONSULTANT: 'CONSULTANT'
};

exports.BusinessType = exports.$Enums.BusinessType = {
  SMALL_BUSINESS: 'SMALL_BUSINESS',
  STARTUP: 'STARTUP',
  ENTERPRISE: 'ENTERPRISE'
};

exports.ListingCategory = exports.$Enums.ListingCategory = {
  ECOMMERCE: 'ECOMMERCE',
  MOBILE: 'MOBILE',
  FINTECH: 'FINTECH'
};

exports.ListingStatus = exports.$Enums.ListingStatus = {
  PENDING: 'PENDING',
  APPROVED: 'APPROVED',
  REJECTED: 'REJECTED'
};

exports.InterestStatus = exports.$Enums.InterestStatus = {
  PENDING: 'PENDING',
  APPROVED: 'APPROVED',
  REJECTED: 'REJECTED'
};

exports.Prisma.ModelName = {
  User: 'User',
  UserProfile: 'UserProfile',
  Subscription: 'Subscription',
  UserRoleAssignment: 'UserRoleAssignment',
  NotificationSettings: 'NotificationSettings',
  Document: 'Document',
  Company: 'Company',
  Listing: 'Listing',
  ListingInterest: 'ListingInterest',
  ListingFinancialMetric: 'ListingFinancialMetric',
  MonthlyFinancial: 'MonthlyFinancial',
  Admin: 'Admin',
  PasswordReset: 'PasswordReset',
  TempUser: 'TempUser'
};

/**
 * This is a stub Prisma Client that will error at runtime if called.
 */
class PrismaClient {
  constructor() {
    return new Proxy(this, {
      get(target, prop) {
        let message
        const runtime = getRuntime()
        if (runtime.isEdge) {
          message = `PrismaClient is not configured to run in ${runtime.prettyName}. In order to run Prisma Client on edge runtime, either:
- Use Prisma Accelerate: https://pris.ly/d/accelerate
- Use Driver Adapters: https://pris.ly/d/driver-adapters
`;
        } else {
          message = 'PrismaClient is unable to run in this browser environment, or has been bundled for the browser (running in `' + runtime.prettyName + '`).'
        }

        message += `
If this is unexpected, please open an issue: https://pris.ly/prisma-prisma-bug-report`

        throw new Error(message)
      }
    })
  }
}

exports.PrismaClient = PrismaClient

Object.assign(exports, Prisma)
