import axios from 'axios'
import { getCurrentUserId } from './auth.service'
import AsyncStorage from '@react-native-async-storage/async-storage'

const API_BASE_URL = 'http://localhost:5000/api/v1'

interface ApiResponse<T> {
  success: boolean
  message: string
  data: T
}

export interface Document {
  name: string
  url: string
  size: string
}

export interface CompanyInfo {
  id: string
  name: string
  industry: string
  status: string
  size: string
  revenue: string
  location: string
  description: string
  businessType?: string // Add businessType field
  logo?: string
  documents: Document[]
  website?: string
  established?: string
}

export interface UserProfile {
  id: string
  name: string
  email: string
  phoneNumber?: string
  status?: string
  createdAt?: string
  updatedAt?: string
  profile?: {
    id?: string
    userId?: string
    imageUrl?: string
    professionalHeadline?: string
    industrySpecialization?: string
    areasOfExpertise?: string[]
    portfolioLink?: string
    introduction?: string
    rating?: number
    hourlyRate?: number
    createdAt?: string
    updatedAt?: string
  }
  company?: CompanyInfo
  roles?: string[]
}

async function fetchFromApi<T>(endpoint: string, errorMsg: string): Promise<T> {
  try {
    const userId = await getCurrentUserId()
    console.log('fetchFromApi - userId:', userId)

    if (!userId) {
      throw new Error('User ID not found. Please log in again.')
    }

    const token = await AsyncStorage.getItem('authToken')
    console.log('fetchFromApi - token exists:', !!token)

    if (!token) {
      throw new Error('Authorization token not found. Please log in again.')
    }

    const fullUrl = `${API_BASE_URL}${endpoint}`
    console.log('fetchFromApi - Making request to:', fullUrl)

    const response = await axios.get<ApiResponse<T>>(
      fullUrl,
      {
        headers: {
          Authorization: `Bearer ${token}`,
          'Content-Type': 'application/json',
        },
      }
    )

    console.log('fetchFromApi - Response status:', response.status)
    console.log('fetchFromApi - Response data:', response.data)

    return response.data.data
  } catch (error) {
    console.error(`Error: ${errorMsg}`, error)

    if (axios.isAxiosError(error) && error.response?.status === 401) {
      throw new Error('Your session has expired. Please log in again.')
    }

    const message =
      axios.isAxiosError(error) && error.response?.data?.message
        ? error.response.data.message
        : error instanceof Error
          ? error.message
          : errorMsg

    throw new Error(message)
  }
}

export async function getUserProfile(): Promise<UserProfile> {
  const userId = await getCurrentUserId()
  return fetchFromApi<UserProfile>(
    `/users/${userId}`,
    'Failed to fetch profile data'
  )
}

export async function getCompanyInfo(): Promise<CompanyInfo> {
  const userData = await getUserProfile()

  if (!userData.company) {
    throw new Error('No company information found.')
  }

  return userData.company
}

export const userService = {
  getUserProfile,
  getCompanyInfo,
}
