'use client'

import { useState } from 'react'
import {
  View,
  Text,
  TouchableOpacity,
  SafeAreaView,
  ScrollView,
  Alert,
} from 'react-native'
import { useRouter } from 'expo-router'
import Link from 'expo-router/link'
import { Feather } from '@expo/vector-icons'
import { Input } from '@/components/auth/Input'
import PhoneInput from '@/components/auth/PhoneInput'
import { Button } from '@/components/auth/Button'
import { HeaderImage } from '@/components/auth/HeaderImage'
import tw from '@/lib/tailwind'
import { colors } from '@/constants/colors'
import { useForm, Controller } from 'react-hook-form'
import { z } from 'zod'
import { zodResolver } from '@hookform/resolvers/zod'
import api from '@/lib/api'
import { useSignupGuard } from '@/hooks/useNavigationGuard'
import { GuardedScreen } from '@/components/guards/GuardedScreen'

// Define validation schema using zod
const signupSchema = z.object({
  name: z.string().min(1, 'Name is required'),
  phone: z.string().min(1, 'Phone number is required'),
  email: z.string().min(1, 'Email is required').email('Invalid email format'),
  password: z
    .string()
    .min(1, 'Password is required')
    .min(8, 'Password must be at least 8 characters'),
})

type SignupFormData = z.infer<typeof signupSchema>

export default function SignupScreen() {
  const router = useRouter()
  const [loading, setLoading] = useState(false)

  // Navigation guard - prevent access if already logged in
  const { canAccess, isLoading: guardLoading } = useSignupGuard()

  // Setup form with validation
  const {
    control,
    handleSubmit,
    formState: { errors },
    setError,
  } = useForm<SignupFormData>({
    resolver: zodResolver(signupSchema),
    defaultValues: {
      name: '',
      phone: '',
      email: '',
      password: '',
    },
  })

  const handleSignup = async (data: SignupFormData) => {
    try {
      setLoading(true)

      // Make API call to register endpoint with the appropriate configuration
      const response = await api.post('/auth/register', {
        name: data.name,
        email: data.email,
        password: data.password,
        phone: data.phone,
      }, {
        // For web, ensure we're using the right CORS mode
        headers: {
          'Content-Type': 'application/json',
        },
      })

      console.log('Registration successful:', response.data)

      // Navigate to verification screen with email parameter
      router.push(`/(auth)/verify-otp?email=${encodeURIComponent(data.email)}&type=signup`)
    } catch (error: any) {
      console.error('Registration error:', error.response?.data || error)

      // Check if we have a 409 Conflict (user already exists)
      if (error.response?.status === 409) {
        // Set a specific error for email field
        setError('email', {
          message: 'Email already registered. Please use a different email.',
        })
        return
      }
      
      // Check if response is HTML (some backend errors come as HTML)
      const responseData = error.response?.data
      if (typeof responseData === 'string' && responseData.includes('<!DOCTYPE html>')) {
        // Try to extract error message from HTML
        const errorMatch = responseData.match(/<pre>ApiError:([^<]+)/)
        if (errorMatch && errorMatch[1]) {
          const errorMessage = errorMatch[1].trim()
          if (errorMessage.includes('email already exists')) {
            setError('email', {
              message: 'Email already registered. Please use a different email.',
            })
          } else {
            Alert.alert('Registration Failed', errorMessage)
          }
          return
        }
      }
      
      // Handle standard JSON API errors
      if (error.response?.data?.errors) {
        // Map backend validation errors to form fields
        const backendErrors = error.response.data.errors

        Object.keys(backendErrors).forEach((key) => {
          if (key === 'email') {
            setError('email', {
              message: backendErrors[key] || 'Email is already registered',
            })
          } else if (key in data) {
            // @ts-ignore - dynamic key access
            setError(key, { message: backendErrors[key] })
          }
        })
      } else if (error.response?.data?.message) {
        // Show general error message
        Alert.alert('Registration Failed', error.response.data.message)
      } else {
        Alert.alert(
          'Registration Failed',
          'An error occurred during registration. Please try again.'
        )
      }
    } finally {
      setLoading(false)
    }
  }

  return (
    <GuardedScreen
      isLoading={guardLoading}
      canAccess={canAccess}
      loadingText="Checking registration status..."
      accessDeniedText="Already registered. Redirecting...">
      <SafeAreaView style={tw`flex-1`}>
        <ScrollView
          contentContainerStyle={tw`flex-grow px-6 pt-4 justify-between bg-white`}>
        <View>
          {/* Top section with image */}
          <HeaderImage />

          {/* Middle section with inputs */}
          <View style={tw`flex-1 justify-center`}>
            <View style={tw`mb-6 mt-2`}>
              <Link href="/(auth)/login" asChild>
                <TouchableOpacity style={tw`flex-row items-center`}>
                  <Feather
                    name="chevron-left"
                    size={20}
                    color={colors.primary.DEFAULT}
                  />
                  <Text
                    style={{ color: colors.primary.DEFAULT, marginLeft: 4 }}>
                    Return to Sign in
                  </Text>
                </TouchableOpacity>
              </Link>
            </View>

            <Controller
              control={control}
              name="name"
              render={({ field: { onChange, value } }) => (
                <Input
                  placeholder="Name"
                  value={value}
                  onChangeText={onChange}
                  autoCapitalize="words"
                  error={errors.name?.message}
                />
              )}
            />

            <Controller
              control={control}
              name="phone"
              render={({ field: { onChange, value } }) => (
                <PhoneInput
                  value={value}
                  onChangeText={onChange}
                  error={errors.phone?.message}
                />
              )}
            />

            <Controller
              control={control}
              name="email"
              render={({ field: { onChange, value } }) => (
                <Input
                  placeholder="Email"
                  value={value}
                  onChangeText={onChange}
                  keyboardType="email-address"
                  autoCapitalize="none"
                  error={errors.email?.message}
                />
              )}
            />

            <Controller
              control={control}
              name="password"
              render={({ field: { onChange, value } }) => (
                <Input
                  placeholder="Password"
                  value={value}
                  onChangeText={onChange}
                  secureTextEntry
                  error={errors.password?.message}
                />
              )}
            />
          </View>
        </View>

        {/* Bottom section with button */}
        <View style={tw`mb-12`}>
          <Button
            title="Sign Up"
            onPress={handleSubmit(handleSignup)}
            loading={loading}
            variant="primary"
          />
        </View>
      </ScrollView>
    </SafeAreaView>
    </GuardedScreen>
  )
}
