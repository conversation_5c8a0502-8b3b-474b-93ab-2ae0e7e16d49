/*
  Warnings:

  - The values [SMALL_BUSINESS] on the enum `ListingCategory` will be removed. If these variants are still used in the database, this will fail.

*/
-- AlterEnum
BEGIN;
CREATE TYPE "ListingCategory_new" AS ENUM ('ECOMMERCE', 'M<PERSON><PERSON><PERSON>', 'FINTECH');
ALTER TABLE "listings" ALTER COLUMN "category" DROP DEFAULT;
ALTER TABLE "listings" ALTER COLUMN "category" TYPE "ListingCategory_new" USING ("category"::text::"ListingCategory_new");
ALTER TYPE "ListingCategory" RENAME TO "ListingCategory_old";
ALTER TYPE "ListingCategory_new" RENAME TO "ListingCategory";
DROP TYPE "ListingCategory_old";
ALTER TABLE "listings" ALTER COLUMN "category" SET DEFAULT 'ECOMMERCE';
COMMIT;

-- AlterTable
ALTER TABLE "listings" ALTER COLUMN "category" SET DEFAULT 'ECOMMERCE';
