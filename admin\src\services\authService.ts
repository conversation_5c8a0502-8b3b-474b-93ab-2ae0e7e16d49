import { set<PERSON><PERSON>ie, destroy<PERSON><PERSON>ie, parseCookies } from "nookies";

// Define the base URL for API calls
const API_BASE_URL =
  process.env.NEXT_PUBLIC_API_URL || "http://localhost:5000/api";

// Define types
export interface User {
  id: string;
  email: string;
  name: string;
  role: string;
  image?: string;
  phone?: string;
  firstName?: string;
  lastName?: string;
  address?: string;
  location?: string;
  company?: string;
  website?: string;
  bio?: string;
  createdAt?: string;
  updatedAt?: string;
}

export interface AuthResponse {
  token: string;
  user: User;
}

export interface ApiResponse<T> {
  success: boolean;
  message: string;
  data: T | null;
}

export interface UserUpdatePayload {
  firstName?: string;
  lastName?: string;
  name?: string;
  email?: string;
  phone?: string;
  address?: string;
  location?: string;
  company?: string;
  website?: string;
  bio?: string;
}

export interface PasswordChangePayload {
  currentPassword: string;
  newPassword: string;
  confirmPassword: string;
}

/**
 * Helper function to make API requests with proper headers and error handling
 */
async function apiRequest<T>(
  endpoint: string,
  options: RequestInit = {}
): Promise<T> {
  const cookies = parseCookies();
  const token = cookies.authToken;

  const headers = new Headers(options.headers || {});
  headers.set("Content-Type", "application/json");

  if (token) {
    headers.set("Authorization", `Bearer ${token}`);
  }

  const config: RequestInit = {
    ...options,
    headers,
  };

  const response = await fetch(`${API_BASE_URL}${endpoint}`, config);

  if (!response.ok) {
    const errorData = await response.json().catch(() => ({
      message: `HTTP error ${response.status}`,
    }));
    throw new Error(errorData.message || `HTTP error ${response.status}`);
  }

  return (await response.json()) as T;
}

/**
 * Authentication Service for Admin Panel
 */
export const AuthService = {
  /**
   * Login with email and password
   * @param email User's email address
   * @param password User's password
   * @param rememberMe Whether to keep the user logged in for a longer period
   */
  login: async (email: string, password: string, rememberMe: boolean = false): Promise<AuthResponse> => {
    try {
      const response = await apiRequest<ApiResponse<AuthResponse>>(
        "/api/v1/admin/login",
        {
          method: "POST",
          body: JSON.stringify({
            email,
            password,
          }),
        }
      );

      if (!response.success || !response.data) {
        throw new Error(response.message || "Login failed");
      }

      const { token, user } = response.data;

      // Store auth token in cookies with expiration based on rememberMe
      setCookie(null, "authToken", token, {
        maxAge: rememberMe ? 30 * 24 * 60 * 60 : 8 * 60 * 60, // 30 days if rememberMe, otherwise 8 hours
        path: "/",
        secure: process.env.NODE_ENV === "production",
        sameSite: "strict",
      });

      // Store rememberMe preference in localStorage
      if (typeof window !== "undefined") {
        localStorage.setItem("rememberMe", rememberMe ? "true" : "false");
      }

      return { token, user };
    } catch (error) {
      if (error instanceof Error) {
        throw error;
      }
      throw new Error("An unknown error occurred during login");
    }
  },

  /**
   * Logout the current user
   */
  logout: async (): Promise<void> => {
    // Clear the auth token cookie
    destroyCookie(null, "authToken", { path: "/" });
    
    // Clear the rememberMe preference from localStorage
    if (typeof window !== "undefined") {
      localStorage.removeItem("rememberMe");
    }
  },

  /**
   * Send a password reset request (forgot password)
   */
  forgotPassword: async (email: string): Promise<{ message: string }> => {
    try {
      const response = await apiRequest<ApiResponse<null>>(
        "/api/v1/admin/forgot-password",
        {
          method: "POST",
          body: JSON.stringify({ email }),
        }
      );

      return {
        message:
          response.message ||
          "If an account exists, you will receive an email with instructions",
      };
    } catch (error) {
      // Even if there's an error, we return a generic message for security
      return {
        message:
          "If an account exists, you will receive an email with instructions",
      };
    }
  },

  /**
   * Verify OTP code sent to email
   */
  verifyOTP: async (
    email: string,
    otp: string
  ): Promise<{ verified: boolean }> => {
    try {
      const response = await apiRequest<ApiResponse<{ verified: boolean }>>(
        "/api/v1/admin/verify-otp",
        {
          method: "POST",
          body: JSON.stringify({
            email,
            otp,
          }),
        }
      );

      if (!response.success || !response.data) {
        throw new Error(response.message || "OTP verification failed");
      }

      return response.data;
    } catch (error) {
      if (error instanceof Error) {
        throw error;
      }
      throw new Error("An unknown error occurred during OTP verification");
    }
  },

  /**
   * Reset password with new password after OTP verification
   */
  resetPassword: async (
    email: string,
    newPassword: string
  ): Promise<{ message: string }> => {
    try {
      const response = await apiRequest<ApiResponse<null>>(
        "/api/v1/admin/reset-password",
        {
          method: "POST",
          body: JSON.stringify({
            email,
            newPassword,
          }),
        }
      );

      return {
        message: response.message || "Password reset successfully",
      };
    } catch (error) {
      if (error instanceof Error) {
        throw error;
      }
      throw new Error("An unknown error occurred during password reset");
    }
  },

  /**
   * Check if user is authenticated
   */
  isAuthenticated: (): boolean => {
    const cookies = parseCookies();
    return !!cookies.authToken;
  },

  /**
   * Get current user from token
   */
  getCurrentUser: async (): Promise<User | null> => {
    if (!AuthService.isAuthenticated()) {
      return null;
    }

    try {
      const cookies = parseCookies();
      const token = cookies.authToken;

      if (!token) {
        return null;
      }

      // Decode the token to get the user ID
      // Note: This assumes the token is a JWT with a payload that includes the user ID
      const tokenParts = token.split(".");
      if (tokenParts.length !== 3) {
        throw new Error("Invalid token format");
      }

      const payload = JSON.parse(atob(tokenParts[1]));
      const userId = payload.id || payload.userId || payload.sub;

      if (!userId) {
        throw new Error("User ID not found in token");
      }

      // Use the updated endpoint to fetch user data
      const response = await apiRequest<ApiResponse<User>>(
        `/api/v1/admin/me/${userId}`,
        {
          method: "GET",
        }
      );

      if (!response.success || !response.data) {
        throw new Error("Failed to get user data");
      }

      return response.data;
    } catch (error) {
      console.error("Error fetching current user:", error);
      return null;
    }
  },

  /**
   * Update user profile information
   */
  updateProfile: async (userData: UserUpdatePayload): Promise<User> => {
    try {
      const cookies = parseCookies();
      const token = cookies.authToken;

      if (!token) {
        throw new Error("No authentication token found");
      }

      // Decode the token to get the user ID
      const tokenParts = token.split(".");
      if (tokenParts.length !== 3) {
        throw new Error("Invalid token format");
      }

      const payload = JSON.parse(atob(tokenParts[1]));
      const userId = payload.id || payload.userId || payload.sub;

      if (!userId) {
        throw new Error("User ID not found in token");
      }

      // Prepare the update data
      const updateData = { ...userData };

      // If firstName and lastName are provided, combine them into name
      if (updateData.firstName || updateData.lastName) {
        updateData.name = `${updateData.firstName || ""} ${
          updateData.lastName || ""
        }`.trim();
      }

      const response = await apiRequest<ApiResponse<User>>(
        `/api/v1/admin/me/${userId}`,
        {
          method: "PATCH",
          body: JSON.stringify(updateData),
        }
      );

      if (!response.success || !response.data) {
        throw new Error(response.message || "Profile update failed");
      }

      return response.data;
    } catch (error) {
      if (error instanceof Error) {
        throw error;
      }
      throw new Error("An unknown error occurred during profile update");
    }
  },

  /**
   * Change user password
   */
  changePassword: async (
    passwordData: PasswordChangePayload
  ): Promise<{ message: string }> => {
    try {
      const cookies = parseCookies();
      const token = cookies.authToken;

      if (!token) {
        throw new Error("No authentication token found");
      }

      // Decode the token to get the user ID
      const tokenParts = token.split(".");
      if (tokenParts.length !== 3) {
        throw new Error("Invalid token format");
      }

      const payload = JSON.parse(atob(tokenParts[1]));
      const userId = payload.id || payload.userId || payload.sub;

      if (!userId) {
        throw new Error("User ID not found in token");
      }

      // Validate passwords match
      if (passwordData.newPassword !== passwordData.confirmPassword) {
        throw new Error("New passwords do not match");
      }

      const response = await apiRequest<ApiResponse<null>>(
        `/api/v1/admin/change-password/${userId}`,
        {
          method: "PUT",
          body: JSON.stringify({
            currentPassword: passwordData.currentPassword,
            newPassword: passwordData.newPassword,
          }),
        }
      );

      return {
        message: response.message || "Password changed successfully",
      };
    } catch (error) {
      if (error instanceof Error) {
        throw error;
      }
      throw new Error("An unknown error occurred during password change");
    }
  },
};

export default AuthService;
