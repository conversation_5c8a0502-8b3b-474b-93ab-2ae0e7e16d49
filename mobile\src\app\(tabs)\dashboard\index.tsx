import { useState } from 'react'
import { View, ScrollView, SafeAreaView } from 'react-native'
import { useRouter } from 'expo-router'
import { setActiveListingTab, setCurrentNavTab } from '@/utils/navigationState'
import tw from '@/lib/tailwind'
import listingData from '@/../data/listing.json'
import { UserGreeting } from '@/components/Dashboard/UserGreeting'
import { SearchInput } from '@/components/Listing/SearchInput'
import { StatCard } from '@/components/Dashboard/StatCard'
import { SectionHeader } from '@/components/Dashboard/SelectionHeader'
import { ListingCard } from '@/components/Listing/ListingCard'
import { CompactListingCard } from '@/components/Listing/CompactListingCard'
import { useProtectedPageGuard } from '@/hooks/useNavigationGuard'
import { GuardedScreen } from '@/components/guards/GuardedScreen'
import { ConsultantProfile } from '@/components/Dashboard/ConsultantProfile'
import { colors } from '@/constants/colors'
import Navbar from '@/components/Navbar'
import FilterModal, { FilterValues } from '@/components/Dashboard/FilterModal'
import { mockUserProfile } from 'data/profileData'

type Consultant = {
  id: string
  name: string
  image: string
}

const consultants: Consultant[] = [
  { id: '1', name: 'Amanda', image: 'https://i.pravatar.cc/150?img=1' },
  { id: '2', name: 'Anderson', image: 'https://i.pravatar.cc/150?img=2' },
  { id: '3', name: 'Samantha', image: 'https://i.pravatar.cc/150?img=3' },
  { id: '4', name: 'Andrew', image: 'https://i.pravatar.cc/150?img=4' },
]

export default function DashboardScreen() {
  const router = useRouter()
  const [searchQuery, setSearchQuery] = useState('')
  const [showFilterModal, setShowFilterModal] = useState(false)

  // Navigation guard - require authentication and completed onboarding
  const { canAccess, isLoading } = useProtectedPageGuard()
  const [filterValues, setFilterValues] = useState<FilterValues>({
    industries: [],
    priceRange: {
      min: '',
      max: '',
    },
    businessType: 'Startup',
    investmentType: 'Full Sale',
  })
  // Convert string status to the proper literal type
  const [listings, setListings] = useState(
    listingData.listings.map((listing) => ({
      ...listing,
      status: listing.status as 'Approved' | 'Pending' | 'Declined',
    }))
  )

  const handleListingPress = (id: string) => {
    router.push(`listing/${id}`)
  }

  const handleFavorite = (id: string) => {
    setListings(
      listings.map((listing) =>
        listing.id === id
          ? { ...listing, isFavorite: !listing.isFavorite }
          : listing
      )
    )
  }
  const handleViewAllListings = () => {
    // Set listing tab to my-listing
    setActiveListingTab('my-listing')
    // Update navbar active tab
    setCurrentNavTab('/listing')
    router.push('/listing')
  }

  const handleViewAllNearby = () => {
    // Set listing tab to browse
    setActiveListingTab('browse')
    // Update navbar active tab
    setCurrentNavTab('/listing')
    router.push('/listing')
  }

  const handleViewAllConsultants = () => {
    // Update navbar active tab
    setCurrentNavTab('/consultants')
    router.push('/consultants')
  }

  const handleConsultantPress = (id: string) => {
    router.push(`/consultant/${id}`)
  }

  return (
    <GuardedScreen
      isLoading={isLoading}
      canAccess={canAccess}
      loadingText="Loading dashboard..."
      accessDeniedText="Redirecting to login...">
      <SafeAreaView style={tw`flex-1`}>
        <View style={tw`flex-1`}>
          <ScrollView style={tw`flex-1`}>
          <View style={tw`pt-4 bg-white px-4 pb-8`}>
            <UserGreeting
              name={`${mockUserProfile[0].firstName} ${mockUserProfile[0].lastName}`}
              avater={mockUserProfile[0].profileImage}
              subtitle="What would you like to consult today?"
              onNotificationPress={() => {}}
            />
            <View style={tw`py-4`}>
              <SearchInput
                value={searchQuery}
                onChangeText={setSearchQuery}
                placeholder="Search listings..."
                onFilterPress={() => setShowFilterModal(true)}
              />
            </View>
            <View style={tw`pb-4`}>
              <View style={tw`flex-row justify-between mb-6`}>
                <StatCard label="Active Listings" value="4" />
                <StatCard label="Investor Inquiries" value="24" />
              </View>

              <View style={tw`mb-6`}>
                <SectionHeader
                  title="Your Listings"
                  onViewAll={handleViewAllListings}
                />
                <ListingCard
                  listing={listings[0]}
                  onPress={() => handleListingPress(listings[0].id)}
                  onMenuPress={(listing) => handleFavorite(listing.id)}
                />
              </View>

              <View style={[tw`mb-4 font-medium`, { color: colors.gray[600] }]}>
                <SectionHeader
                  title="Explore Nearby Listing"
                  onViewAll={handleViewAllNearby}
                />

                <View style={tw`flex flex-wrap flex-row justify-between mb-4`}>
                  {listings.slice(0, 4).map((listing) => (
                    <View key={listing.id} style={tw``}>
                      <CompactListingCard
                        listing={listing}
                        onPress={handleListingPress}
                        onFavorite={() => handleFavorite(listing.id)}
                        isFavorite={listing.isFavorite}
                      />
                    </View>
                  ))}
                </View>
              </View>

              <View style={tw`mb-4`}>
                <SectionHeader
                  title="Top Consultants"
                  onViewAll={handleViewAllConsultants}
                />

                <View style={tw`flex-row justify-between mb-8`}>
                  {consultants.map((item: Consultant) => (
                    <ConsultantProfile
                      key={item.id}
                      id={item.id}
                      name={item.name}
                      image={item.image}
                      onPress={() => handleConsultantPress(item.id)}
                    />
                  ))}
                </View>
              </View>
            </View>
          </View>
        </ScrollView>
        {/* Custom Bottom Navbar - Fixed at bottom */}
        <View style={tw`absolute bottom-0 left-0 right-0 px-4`}>
          <Navbar />
        </View>
      </View>

      {/* Filter Modal */}
      <FilterModal
        visible={showFilterModal}
        onClose={() => setShowFilterModal(false)}
        onApply={(filters) => {
          setFilterValues(filters)
          setShowFilterModal(false)
          // Here you can apply the filters to your listings
          console.log('Applied filters:', filters)
        }}
        initialFilters={filterValues}
      />
    </SafeAreaView>
    </GuardedScreen>
  )
}
