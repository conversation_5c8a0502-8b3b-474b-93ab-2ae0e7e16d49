import { PrismaClient } from "@prisma/client";
import { Request } from "express";

const prisma = new PrismaClient();

const createSubscription = async (req: Request) => {
  const { planType, userId, price, period, description, features } = req.body;

  // Verify admin is making this request
  if (!req.admin) {
    throw new Error('Admin authentication required for subscription creation');
  }

  // Validate required fields
  if (!planType) {
    throw new Error('Plan type is required');
  }

  if (!userId) {
    throw new Error('User ID is required');
  }

  const newSubscription = await prisma.subscription.create({
    data: {
      planType,
      userId,
      price,
      period,
      description,
      features: features || [],
    },
  });

  return newSubscription;
};

const updateSubscription = async (req: Request) => {
  const { id } = req.params;
  const { planType, price, period, description, features } = req.body;

  const updatedSubscription = await prisma.subscription.update({
    where: { id },
    data: {
      planType,
      price,
      period,
      description,
      features,
    },
  });

  return updatedSubscription;
};

const deleteSubscription = async (req: Request) => {
  const { id } = req.params;

  const deletedSubscription = await prisma.subscription.delete({
    where: { id },
  });

  return deletedSubscription;
};

const getAllSubscription = async (req: Request) => {
  const subscriptions = await prisma.subscription.findMany();
  return subscriptions;
};

const getPlanById = async (req: Request) => {
  const { id } = req.params;

  const subscription = await prisma.subscription.findUnique({
    where: { id },
  });

  return subscription;
};


export const SubscriptionService = {
  createSubscription,
  updateSubscription,
  deleteSubscription,
  getAllSubscription,
  getPlanById,
};
