import express from "express";
import { SubscriptionController } from "./subscription.controller";
import { adminAuth } from "../../middlewares/auth";

const router = express.Router();

// Admin-only route: Create subscription plan
router.post("/", adminAuth(), SubscriptionController.createSubscription);
router.patch(
  "/edit-subscription/:id",
  SubscriptionController.updateSubscription
);
router.delete("/:id", SubscriptionController.deleteSubscription);
router.get("/", SubscriptionController.getAllSubscription);
router.get("/:id", SubscriptionController.getPlanById);

export const SubscriptionRoutes = router;
