import React, { useState, useEffect } from 'react'
import { View, TouchableOpacity, Text, ActivityIndicator } from 'react-native'
import { Feather } from '@expo/vector-icons'
import { useForm, FormProvider } from 'react-hook-form'
import Toast from 'react-native-toast-message'
import tw from '@/lib/tailwind'
import { RevenueBreakdownPage } from '@/components/Listing/add-edit-listing/RevenueBreakdownPage'
import { SuccessModal } from '@/components/Listing/add-edit-listing/SuccessModal'
import { GeneralInfoPage } from '@/components/Listing/add-edit-listing/GeneralInfoPage'
import useListingStore from '@/store/useListingStore'
import api from '@/lib/api'
import { userService } from '@/services/user.service'
import { ConfidentialDocumentsPage } from '@/components/Listing/add-edit-listing/ConfidentialDocumentsPage'
import { Document } from '@/../types/document'
import { getCurrentUserId } from '@/services/auth.service'
import AsyncStorage from '@react-native-async-storage/async-storage'

type ListingFormData = {
  // General Info
  title: string
  location: string
  tags: string[]
  category: string // Add category field
  coverImage: string | null
  visibility: string
  description: string
  companyName: string
  industry: string
  businessType: string
  established: string
  employees: string
  askingPrice: string
  equityOffered: string
  revenue: string
  profitMargin: string
  growthRate: string

  // Financial metrics
  monthlyRevenue: Record<string, string>
  ebitda: string
  revenueYoY: string
  ebitdaYoY: string
  profitMarginYoY: string

  // Documents
  documents: Document[]
}

export default function AddNewListingScreen({ navigation }: any) {
  const [currentPage, setCurrentPage] = useState(1)
  const [showSuccessModal, setShowSuccessModal] = useState(false)
  const [progress, setProgress] = useState(25)
  const [fetchingCompany, setFetchingCompany] = useState(true)
  const [companyData, setCompanyData] = useState<any>(null)
  const [disabledFields, setDisabledFields] = useState<string[]>([]) // Fields to disable

  // Get store methods
  const { createListing, loading, error } = useListingStore((state) => state)
  const [userId, setUserId] = useState<string | null>(null)
  const [isLoadingUserId, setIsLoadingUserId] = useState(true)

  // Get user ID on component mount
  useEffect(() => {
    const fetchUserId = async () => {
      try {
        setIsLoadingUserId(true)
        const id = await getCurrentUserId()
        setUserId(id)
        console.log('User data:', id) // Now this will log the actual user ID
      } catch (error) {
        console.error('Error getting user ID:', error)
      } finally {
        setIsLoadingUserId(false)
      }
    }

    fetchUserId()
  }, [])

  // Fetch company data when userId becomes available
  useEffect(() => {
    const fetchCompanyData = async () => {
      console.log('fetchCompanyData called with userId:', userId)

      if (!userId) {
        // Don't log anything here - this is expected when userId is not yet loaded
        return
      }

      try {
        setFetchingCompany(true)
        const company = await userService.getCompanyInfo()

        console.log('Fetched company data:', company)
        setCompanyData(company)

        // Pre-fill form with company data
        const fieldsToDisable: string[] = []
        const updates: any = {}

        // Map company data to form fields
        if (company) {
          // Explicitly map company data fields to form fields
          // This avoids TypeScript errors with string indexing
          if (company.name) {
            updates['companyName'] = company.name
            fieldsToDisable.push('companyName')
          }

          if (company.location) {
            updates['location'] = company.location
            fieldsToDisable.push('location')
          }

          if (company.industry) {
            updates['industry'] = company.industry
            fieldsToDisable.push('industry')
          }

          if (company.size) {
            updates['employees'] = company.size
            fieldsToDisable.push('employees')
          }

          if (company.description) {
            updates['description'] = company.description
            fieldsToDisable.push('description')
          }

          if (company.established) {
            updates['established'] = company.established
            fieldsToDisable.push('established')
          }

          // Adding equityOffered if it exists in the company data
          const anyCompany = company as any
          if (anyCompany.equityOffered) {
            updates['equityOffered'] = anyCompany.equityOffered
            fieldsToDisable.push('equityOffered')
          }

          // Set form values and update disabled fields
          Object.entries(updates).forEach(([key, value]) => {
            methods.setValue(key as any, value as any)
          })

          setDisabledFields(fieldsToDisable)
        }
      } catch (error) {
        console.error('Error fetching company data:', error)

        // More detailed error logging to help debug
        if (error instanceof Error) {
          console.error('Error message:', error.message)
          console.error('Error stack:', error.stack)

          // Check if it's a "No company information found" error
          if (error.message.includes('No company information found')) {
            console.log('User has no company data - this is normal for new users')
            // Don't show an error toast for this case, just continue without company data
          } else {
            // Show error toast for other types of errors
            Toast.show({
              type: 'error',
              text1: 'Error',
              text2: error.message,
              position: 'bottom',
            })
          }
        } else {
          // Check if it's an axios error with response details
          if (error && typeof error === 'object' && 'response' in error) {
            const axiosError = error as any
            console.error('Response status:', axiosError.response?.status)
            console.error('Response data:', axiosError.response?.data)
            console.error('Response headers:', axiosError.response?.headers)
          }

          // Check if it's a network error
          if (error && typeof error === 'object' && 'code' in error) {
            console.error('Network error code:', (error as any).code)
          }

          Toast.show({
            type: 'error',
            text1: 'Error',
            text2: 'Failed to fetch company data',
            position: 'bottom',
          })
        }
      } finally {
        setFetchingCompany(false)
      }
    }

    // Only fetch company data if userId is available
    if (userId) {
      fetchCompanyData()
    } else if (!isLoadingUserId) {
      // If we're not loading userId anymore and still don't have one, stop loading company data
      setFetchingCompany(false)
    }
  }, [userId, isLoadingUserId])

  // Setup React Hook Form
  const methods = useForm<ListingFormData>({
    defaultValues: {
      // General Info
      title: '',
      location: '',
      tags: ['E-commerce', 'Small Business'],
      category: 'MOBILE', // Default category
      coverImage: null,
      visibility: 'private',
      description: '',
      companyName: '',
      industry: 'technology',
      businessType: 'SMALL_BUSINESS', // Update to match backend enum
      established: '',
      employees: '15-30',
      askingPrice: '',
      equityOffered: '',
      revenue: '',
      profitMargin: '',
      growthRate: '',

      // Monthly Revenue
      monthlyRevenue: {
        Jan: '3000',
        Feb: '0',
        Mar: '0',
        Apr: '0',
        May: '0',
        Jun: '0',
        Jul: '0',
        Aug: '0',
        Sep: '0',
        Oct: '0',
        Nov: '0',
        Dec: '0',
      },

      // Financial metrics
      ebitda: '0',
      revenueYoY: '0',
      ebitdaYoY: '0',
      profitMarginYoY: '0',

      // Documents
      documents: [],
    },
  })

  const { watch, setValue } = methods

  // Create local state from form data for existing components
  const generalInfo = {
    title: watch('title'),
    location: watch('location'),
    tags: watch('tags'),
    category: watch('category'), // Add category
    coverImage: watch('coverImage'),
    visibility: watch('visibility'),
    description: watch('description'),
    companyName: watch('companyName'),
    industry: watch('industry'),
    businessType: watch('businessType'),
    established: watch('established'),
    employees: watch('employees'),
    askingPrice: watch('askingPrice'),
    equityOffered: watch('equityOffered'),
    revenue: watch('revenue'),
    profitMargin: watch('profitMargin'),
    growthRate: watch('growthRate'),
  }

  const monthlyRevenue = watch('monthlyRevenue')
  const financialMetrics = {
    ebitda: watch('ebitda'),
    revenueYoY: watch('revenueYoY'),
    ebitdaYoY: watch('ebitdaYoY'),
    profitMarginYoY: watch('profitMarginYoY'),
  }
  const documents = watch('documents')

  // Display error toast if there's an error from the store
  useEffect(() => {
    if (error) {
      Toast.show({
        type: 'error',
        text1: 'Error',
        text2: error,
        position: 'bottom',
      })
    }
  }, [error])

  // Update handlers to use form setValue
  const handleUpdateGeneralInfo = (key: string, value: any) => {
    setValue(key as any, value)
  }

  const handleUpdateMonthlyRevenue = (month: string, value: string) => {
    setValue(`monthlyRevenue.${month}` as any, value.replace(/[^0-9.]/g, ''))
  }

  const handleUpdateFinancialMetrics = (key: string, value: string) => {
    setValue(key as any, value)
  }

  const setDocuments = (docs: Document[]) => {
    setValue('documents', docs)
  }

  // Update progress indicator based on current page
  useEffect(() => {
    if (currentPage === 1) setProgress(25)
    else if (currentPage === 2) setProgress(50)
    else if (currentPage === 3) setProgress(75)
  }, [currentPage])

  const handleNext = () => {
    if (currentPage < 3) {
      setCurrentPage(currentPage + 1)
    } else {
      // Submit form
      handleSubmitListing()
    }
  }

  const handlePrevious = () => {
    if (currentPage > 1) {
      setCurrentPage(currentPage - 1)
    } else {
      navigation.goBack()
    }
  }

  const handleSuccessModalClose = () => {
    setShowSuccessModal(false)
    navigation.navigate('Listings')
  }

  // Handle form submission and API call
  const handleSubmitListing = async () => {
    try {
      console.log('[AddNewListingScreen] User object:', userId)
      console.log('[AddNewListingScreen] User ID:', userId)

      // Check if user has auth token (same method as onboarding)
      let token = null
      try {
        token = await AsyncStorage.getItem('authToken')
        console.log('[AddNewListingScreen] Retrieved token from AsyncStorage:', !!token)
      } catch (storageError) {
        console.error('[AddNewListingScreen] Error accessing AsyncStorage:', storageError)
      }

      if (!token) {
        console.log('[AddNewListingScreen] No auth token found - user may not be logged in')
      }

      // Validate form data
      const formData = methods.getValues()
      console.log('[AddNewListingScreen] Form data:', formData)

      // Check if we need to update the company data with new values
      const companyUpdates: Record<string, any> = {}

      // Fields that can update the company
      const companyFieldMappings = [
        { form: 'established', company: 'established' },
        { form: 'equityOffered', company: 'equityOffered' },
      ]

      // Check which fields have values but are not in the company data
      companyFieldMappings.forEach((mapping) => {
        const formValue = formData[mapping.form as keyof ListingFormData]
        if (
          formValue &&
          (!companyData ||
            companyData[mapping.company] === null ||
            companyData[mapping.company] === undefined)
        ) {
          companyUpdates[mapping.company] = formValue
        }
      })

      // If we have updates for the company, send them
      if (Object.keys(companyUpdates).length > 0 && userId) {
        console.log(
          '[AddNewListingScreen] Updating company with:',
          companyUpdates
        )

        // Check if we have auth token before attempting update (same method as onboarding)
        let token = null
        try {
          token = await AsyncStorage.getItem('authToken')
          console.log('[AddNewListingScreen] Auth token available for company update:', !!token)
        } catch (storageError) {
          console.error('[AddNewListingScreen] Error accessing AsyncStorage for company update:', storageError)
        }

        if (token) {
          try {
            const companyResponse = await api.patch(
              `/company/user/${userId}`,
              companyUpdates
            )

            if (!companyResponse || !companyResponse.data) {
              console.warn(
                'Company update failed but continuing with listing creation'
              )
            } else {
              console.log('Company updated successfully')
            }
          } catch (error) {
            console.warn('Failed to update company but continuing with listing:', error)
          }
        } else {
          console.log('[AddNewListingScreen] Skipping company update - no auth token available')
        }
      }

      if (!formData.title.trim()) {
        Toast.show({
          type: 'error',
          text1: 'Error',
          text2: 'Listing title is required',
          position: 'bottom',
        })
        setCurrentPage(1) // Go back to first page
        return
      }

      // Validate required fields
      if (!formData.askingPrice || !formData.location) {
        Toast.show({
          type: 'error',
          text1: 'Error',
          text2: 'Please fill in all required fields',
          position: 'bottom',
        })
        return
      }

      // Ensure categoryIds is not empty and contains valid IDs
      if (!formData.tags || formData.tags.length === 0) {
        Toast.show({
          type: 'error',
          text1: 'Error',
          text2: 'Please select at least one category',
          position: 'bottom',
        })
        return
      }

      // Get companyId from user or form
      const companyId = companyData?.id || ''
      console.log('[AddNewListingScreen] CompanyId from user:', userId)
      console.log(
        '[AddNewListingScreen] Final companyId being used:',
        companyId
      )

      if (!companyId) {
        Toast.show({
          type: 'error',
          text1: 'Error',
          text2: 'No company associated with your account',
          position: 'bottom',
        })
        return
      }

      // Prepare the data for API - using new single category enum format
      const listingData = {
        title: formData.title,
        image: formData.coverImage,
        askingPrice: formData.askingPrice,
        category: formData.category, // Use category from form (MOBILE, FINTECH, ECOMMERCE)
        location: formData.location,
        ownerId: userId,

        // Financial data
        ebitda: formData.ebitda,
        revenueYoY: formData.revenueYoY,
        ebitdaYoY: formData.ebitdaYoY,
        marginYoY: formData.profitMarginYoY,

        // Monthly data (transform to array format expected by API)
        monthlyData: Object.entries(formData.monthlyRevenue).map(
          ([month, revenue]) => {
            // Map month names to month numbers (1-12)
            const monthNames = [
              'Jan',
              'Feb',
              'Mar',
              'Apr',
              'May',
              'Jun',
              'Jul',
              'Aug',
              'Sep',
              'Oct',
              'Nov',
              'Dec',
            ]
            const monthNumber = monthNames.findIndex((m) => m === month) + 1

            // Calculate ebitda and profit margin based on revenue (simple calculation)
            const revenueNum = parseFloat(revenue) || 0
            const ebitdaValue = Math.round(revenueNum * 0.25).toString() // 25% of revenue
            const profitMarginValue = "25%" // Default 25%

            return {
              month: monthNumber,
              year: new Date().getFullYear(),
              revenue,
              ebitda: ebitdaValue,
              profitMargin: profitMarginValue,
            }
          }
        ),
      }

      // Call the API through store
      console.log(
        '[AddNewListingScreen] Calling createListing with companyId:',
        companyId
      )
      console.log('[AddNewListingScreen] Listing data being sent:', JSON.stringify(listingData, null, 2))
      const result = await createListing(companyId, listingData)
      console.log('[AddNewListingScreen] Create listing result:', result)

      if (result) {
        setShowSuccessModal(true)
      }
    } catch (err) {
      console.error('Error submitting listing:', err)
      Toast.show({
        type: 'error',
        text1: 'Error',
        text2: 'Failed to create listing. Please try again.',
        position: 'bottom',
      })
    }
  }

  const renderCurrentPage = () => {
    switch (currentPage) {
      case 1:
        return (
          <GeneralInfoPage
            formData={generalInfo}
            setFormData={handleUpdateGeneralInfo}
            disabledFields={disabledFields}
          />
        )
      case 2:
        return (
          <RevenueBreakdownPage
            monthlyRevenue={monthlyRevenue}
            setMonthlyRevenue={handleUpdateMonthlyRevenue}
            financialMetrics={financialMetrics}
            setFinancialMetrics={handleUpdateFinancialMetrics}
          />
        )
      case 3:
        return (
          <ConfidentialDocumentsPage
            documents={documents}
            setDocuments={(documents) => {
              // Ensure consistent document format with name property
              setDocuments(documents as Document[])
            }}
          />
        )
      default:
        return null
    }
  }

  return (
    <FormProvider {...methods}>
      <View style={tw`flex-1 bg-gray-50`}>
        <View style={tw`px-4 py-2 flex-row items-center`}>
          <View style={tw`flex-1 h-2 bg-gray-200 rounded-full`}>
            <View
              style={[
                tw`h-2 bg-blue-900 rounded-full`,
                { width: `${progress}%` },
              ]}
            />
          </View>
        </View>

        {(isLoadingUserId || fetchingCompany) ? (
          <View style={tw`flex-1 justify-center items-center`}>
            <ActivityIndicator size="large" color="#1E3A8A" />
            <Text style={tw`mt-4 text-gray-700`}>
              {isLoadingUserId ? 'Loading user data...' : 'Loading company data...'}
            </Text>
          </View>
        ) : (
          renderCurrentPage()
        )}

        <View
          style={tw`absolute bottom-0 right-0 left-0 p-4 bg-white border-t border-gray-200 flex-row justify-between`}>
          <TouchableOpacity
            style={tw`border border-gray-300 rounded-md py-3 px-6 flex-row items-center`}
            onPress={handlePrevious}>
            <Feather name="chevron-left" size={20} color="#374151" />
            <Text style={tw`text-gray-800 font-medium ml-2`}>Previous</Text>
          </TouchableOpacity>

          <TouchableOpacity
            style={tw`bg-blue-900 rounded-md py-3 px-6 flex-row items-center ${loading ? 'opacity-70' : ''}`}
            onPress={handleNext}
            disabled={loading}>
            {loading ? (
              <ActivityIndicator size="small" color="white" style={tw`mr-2`} />
            ) : (
              <Text style={tw`text-white font-bold mr-2`}>
                {currentPage === 3 ? 'Submit Listing' : 'Next'}
              </Text>
            )}
            {!loading && currentPage < 3 && (
              <Feather name="chevron-right" size={20} color="white" />
            )}
          </TouchableOpacity>
        </View>

        <SuccessModal
          visible={showSuccessModal}
          onClose={handleSuccessModalClose}
        />
      </View>
    </FormProvider>
  )
}
