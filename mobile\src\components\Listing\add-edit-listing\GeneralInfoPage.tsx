import React from "react";
import {
  View,
  Text,
  ScrollView,
  TextInput,
  TouchableOpacity,
} from "react-native";
import { Feather } from "@expo/vector-icons";
import tw from "@/lib/tailwind";

import { Input } from "@/components/form/input";

type GeneralInfoPageProps = {
  formData: {
    title: string;
    location: string;
    tags: string[];
    category: string; // Add category field
    coverImage: string | null;
    visibility: string;
    description: string;
    companyName: string;
    industry: string;
    businessType: string;
    established: string;
    employees: string;
    askingPrice: string;
    equityOffered: string;
    revenue: string;
    profitMargin: string;
    growthRate: string;
  };
  setFormData: (key: string, value: any) => void;
  isEditMode?: boolean;
  disabledFields?: string[];
};

export const GeneralInfoPage = ({
  formData,
  setFormData,
  isEditMode = false,
  disabledFields = [],
}: GeneralInfoPageProps) => {
  // Helper function to check if a field is disabled
  const isDisabled = (fieldName: string): boolean => {
    return disabledFields.includes(fieldName);
  };
  return (
    <ScrollView style={tw`flex-1 px-4`}>
      <Text style={tw`text-xl font-bold mt-4 mb-6`}>General Information</Text>

      {isEditMode && (
        <Input
          label="Thumbnail / Cover Image"
          type="image"
          imageUrl={formData.coverImage || undefined}
          onEditImage={() => {
            setFormData(
              "coverImage",
              "https://images.unsplash.com/photo-1486406146926-c627a92ad1ab?ixlib=rb-4.0.3&ixid=MnwxMjA3fDB8MHxwaG90by1wYWdlfHx8fGVufDB8fHx8&auto=format&fit=crop&w=2070&q=80"
            );
          }}
        />
      )}

      <Input
        label="Listing Title"
        value={formData.title}
        onChangeText={(value) => setFormData("title", value)}
        placeholder="Enter listing title"
      />

      <Input
        label="Location / Country"
        value={formData.location}
        onChangeText={(value) => setFormData("location", value)}
        placeholder="Enter company location"
        disabled={isDisabled('location')}
        disabledNote={isDisabled('location') ? 'Fetched from company data' : undefined}
      />

      <Input
        label="Category"
        type="select"
        placeholder="Select category"
        selectedValue={formData.category}
        options={[
          { value: "MOBILE", label: "Mobile" },
          { value: "FINTECH", label: "Fintech" },
          { value: "ECOMMERCE", label: "E-commerce" },
        ]}
        onSelect={(value) => setFormData("category", value)}
      />

      <View style={tw`mb-4`}>
        <Text style={tw`text-gray-800 font-medium mb-1`}>
          Tags / Categories
        </Text>
        <View
          style={tw`flex-row flex-wrap items-center border border-gray-300 rounded-md p-2 bg-white`}
        >
          {formData.tags.map((tag, index) => (
            <View
              key={index}
              style={tw`bg-blue-100 flex-row items-center rounded-md mr-2 mb-2 px-2 py-1`}
            >
              <View
                style={{
                  ...tw`w-5 h-5 rounded-full border ${isDisabled('tags') ? 'border-gray-400' : 'border-gray-300'} mr-2 items-center justify-center`,
                  ...(formData.tags.includes(tag) ? tw`border-${isDisabled('tags') ? 'gray-500' : 'blue-600'} border-2` : ''),
                }}
              >
                {formData.tags.includes(tag) && (
                  <View style={tw`w-2.5 h-2.5 rounded-full bg-${isDisabled('tags') ? 'gray-500' : 'blue-600'}`} />
                )}
              </View>
              <Text style={tw`text-blue-800 mr-1`}>{tag}</Text>
              <TouchableOpacity
                onPress={() => {
                  const newTags = [...formData.tags];
                  newTags.splice(index, 1);
                  setFormData("tags", newTags);
                }}
              >
                <Feather name="x" size={16} color="#1e40af" />
              </TouchableOpacity>
            </View>
          ))}
        </View>
      </View>

      {!isEditMode && (
        <Input
          label="Thumbnail / Cover Image"
          type="file"
          value={formData.coverImage || undefined}
          onRightIconPress={() => {
            setFormData("coverImage", "sample-image.jpg");
          }}
        />
      )}

      <Input
        label="Visibility"
        type="radio"
        options={[
          { value: "private", label: "Private" },
          { value: "public", label: "Public" },
        ]}
        selectedValue={formData.visibility}
        onSelect={(value) => setFormData("visibility", value)}
      />

      <View style={tw`w-full`}>
        <Text style={tw`text-xl font-bold mt-6 mb-6`}>Business Description</Text>

        <Input
          label="Short Description"
          value={formData.description}
          onChangeText={(value) => setFormData("description", value)}
          placeholder="Enter business description"
          multiline
          numberOfLines={4}
          textAlignVertical="top"
          style={{ minHeight: 100 }}
          containerStyle="w-full px-2"
          disabled={isDisabled('description')}
          disabledNote={isDisabled('description') ? 'Fetched from company data' : undefined}
        />
      </View>

      <Input
        label="Company Name"
        value={formData.companyName}
        onChangeText={(value) => setFormData("companyName", value)}
        placeholder="Enter company name"
        disabled={isDisabled('companyName')}
        disabledNote={isDisabled('companyName') ? 'Fetched from company data' : undefined}
      />

      <Input
        label="Industry"
        type="select"
        placeholder="Select industry"
        selectedValue={formData.industry}
        options={[
          { value: "technology", label: "Technology" },
          { value: "retail", label: "Retail" },
          { value: "healthcare", label: "Healthcare" },
          { value: "finance", label: "Finance" },
        ]}
        onRightIconPress={() => {}}
        onSelect={(value) => setFormData("industry", value)}
        disabled={isDisabled('industry')}
        disabledNote={isDisabled('industry') ? 'Fetched from company data' : undefined}
      />

      <Input
        label="Business Type"
        type="select"
        placeholder="Select business type"
        selectedValue={formData.businessType}
        options={[
          { value: "SMALL_BUSINESS", label: "Small Business" },
          { value: "STARTUP", label: "Startup" },
          { value: "ENTERPRISE", label: "Enterprise" },
        ]}
        onRightIconPress={() => {}}
        onSelect={(value) => setFormData("businessType", value)}
      />

      <Input
        label="Established"
        value={formData.established}
        onChangeText={(value) => setFormData("established", value)}
        placeholder="eg.2018"
        keyboardType="numeric"
        disabled={isDisabled('established')}
        disabledNote={isDisabled('established') ? 'Fetched from company data' : undefined}
      />

      <Input
        label="Number of Employees"
        type="select"
        placeholder="Select employee count"
        selectedValue={formData.employees}
        options={[
          { value: "1-5", label: "1-5" },
          { value: "6-15", label: "6-15" },
          { value: "15-30", label: "15-30" },
          { value: "31-50", label: "31-50" },
          { value: "50+", label: "50+" },
          { value: "150", label: "150" },
        ]}
        onRightIconPress={() => {}}
        onSelect={(value) => setFormData("employees", value)}
        disabled={isDisabled('employees')}
        disabledNote={isDisabled('employees') ? 'Fetched from company data' : undefined}
      />

      <Text style={tw`text-xl font-bold mt-6 mb-6`}>
        Investment Opportunity
      </Text>

      <Input
        label="Asking Price"
        prefix="$"
        value={formData.askingPrice}
        onChangeText={(value) =>
          setFormData("askingPrice", value.replace(/[^0-9.]/g, ""))
        }
        keyboardType="numeric"
        placeholder="0"
      />

      <Input
        label="Equity Offered"
        suffix="%"
        value={formData.equityOffered}
        onChangeText={(value) =>
          setFormData("equityOffered", value.replace(/[^0-9.]/g, ""))
        }
        keyboardType="numeric"
        placeholder="0"
        disabled={isDisabled('equityOffered')}
        disabledNote={isDisabled('equityOffered') ? 'Fetched from company data' : undefined}
      />

      <Input
        label="Revenue (Annual)"
        prefix="$"
        value={formData.revenue}
        onChangeText={(value) =>
          setFormData("revenue", value.replace(/[^0-9.]/g, ""))
        }
        keyboardType="numeric"
        placeholder="0"
      />

      <Input
        label="Profit Margin"
        suffix="%"
        value={formData.profitMargin}
        onChangeText={(value) =>
          setFormData("profitMargin", value.replace(/[^0-9.]/g, ""))
        }
        keyboardType="numeric"
        placeholder="0"
      />

      <View style={tw`mb-4`}>
        <Text style={tw`text-gray-800 font-medium mb-1`}>Growth Rate</Text>
        <View
          style={tw`flex-row items-center border border-gray-300 rounded-md bg-white`}
        >
          <Text
            style={tw`px-3 py-3 border-r border-gray-300 text-gray-800 font-medium`}
          >
            YoY
          </Text>
          <TextInput
            style={tw`flex-1 p-3 text-gray-800`}
            value={formData.growthRate}
            onChangeText={(value) =>
              setFormData("growthRate", value.replace(/[^0-9.]/g, ""))
            }
            placeholder="0"
            placeholderTextColor="#9ca3af"
            keyboardType="numeric"
          />
          <Text
            style={tw`px-3 py-3 border-l border-gray-300 text-gray-800 font-medium`}
          >
            %
          </Text>
        </View>
      </View>

      <View style={tw`h-24`} />
    </ScrollView>
  );
};
