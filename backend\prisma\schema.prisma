generator client {
  provider = "prisma-client-js"
}

datasource db {
  provider = "postgresql"
  url      = env("DATABASE_URL")
}

model User {
  id                   String                @id @default(uuid())
  name                 String
  email                String                @unique
  phoneNumber          String?
  password             String
  status               UserStatus            @default(PENDING)
  createdAt            DateTime              @default(now())
  updatedAt            DateTime              @updatedAt
  isDeleted            Boolean               @default(false)
  company              Company?
  listings             Listing[]
  interests            ListingInterest[]
  notificationSettings NotificationSettings?
  subscription         Subscription?
  profile              UserProfile?
  roles                UserRoleAssignment[]

  @@map("users")
}

model UserProfile {
  id                     String     @id @default(uuid())
  userId                 String     @unique
  imageUrl               String?
  professionalHeadline   String?
  industrySpecialization String?
  areasOfExpertise       String[]   @default([])
  portfolioLink          String?
  introduction           String?
  rating                 Float?
  hourlyRate             Int?
  createdAt              DateTime   @default(now())
  updatedAt              DateTime   @updatedAt
  documents              Document[]
  user                   User       @relation(fields: [userId], references: [id], onDelete: Cascade)

  @@map("user_profiles")
}

model Subscription {
  id          String    @id @default(uuid())
  userId      String    @unique
  planType    String
  startDate   DateTime  @default(now())
  endDate     DateTime?
  price       String?
  period      String?
  description String?
  features    String[]  @default([])
  createdAt   DateTime  @default(now())
  updatedAt   DateTime  @updatedAt
  user        User      @relation(fields: [userId], references: [id], onDelete: Cascade)

  @@map("subscriptions")
}

model UserRoleAssignment {
  id        String   @id @default(uuid())
  userId    String
  role      UserRole
  createdAt DateTime @default(now())
  user      User     @relation(fields: [userId], references: [id], onDelete: Cascade)

  @@unique([userId, role])
  @@map("user_role_assignments")
}

model NotificationSettings {
  id               String  @id @default(uuid())
  userId           String  @unique
  newMessages      Boolean @default(true)
  newListings      Boolean @default(true)
  investorInterest Boolean @default(true)
  appUpdates       Boolean @default(true)
  marketingEmails  Boolean @default(true)
  user             User    @relation(fields: [userId], references: [id], onDelete: Cascade)

  @@map("notification_settings")
}

model Document {
  id            String       @id @default(uuid())
  userProfileId String?
  companyId     String?
  documentUrl   String
  documentType  String?
  metadata      Json?
  createdAt     DateTime     @default(now())
  updatedAt     DateTime     @default(now()) @updatedAt
  company       Company?     @relation(fields: [companyId], references: [id], onDelete: Cascade)
  userProfile   UserProfile? @relation(fields: [userProfileId], references: [id], onDelete: Cascade)

  @@map("documents")
}

model Company {
  id                 String       @id @default(uuid())
  userId             String       @unique
  name               String
  logo               String?
  industry           String?
  location           String?
  size               String?
  revenue            String?
  status             String?      @default("Active")
  website            String?
  description        String?
  established        String?
  employees          String?
  annualRevenue      String?
  equityOffered      String?
  revenueGrowth      String?
  ebitda             String?
  ebitdaGrowth       String?
  profitMargin       String?
  profitMarginGrowth String?
  businessType       BusinessType @default(SMALL_BUSINESS)
  createdAt          DateTime     @default(now())
  updatedAt          DateTime     @updatedAt
  user               User         @relation(fields: [userId], references: [id], onDelete: Cascade)
  documents          Document[]
  listings           Listing[]

  @@map("companies")
}

model Listing {
  id          String          @id @default(uuid())
  title       String
  image       String?
  askingPrice String
  category    ListingCategory @default(ECOMMERCE)
  status      ListingStatus   @default(PENDING)
  location    String
  description String?
  isFavorite  Boolean         @default(false)
  ownerId     String
  companyId   String
  createdAt   DateTime        @default(now())
  updatedAt   DateTime        @updatedAt

  // Relations
  company             Company                  @relation(fields: [companyId], references: [id], onDelete: Cascade)
  owner               User                     @relation(fields: [ownerId], references: [id], onDelete: Cascade)
  monthlyFinancials   MonthlyFinancial[]
  financialMetric     ListingFinancialMetric?
  interests           ListingInterest[]

  @@map("listings")
}

model ListingInterest {
  id        String        @id @default(uuid())
  listingId String
  userId    String        // The investor who showed interest
  status    InterestStatus @default(PENDING)
  message   String?       // Optional message from investor
  createdAt DateTime      @default(now())
  updatedAt DateTime      @updatedAt

  // Relations
  listing   Listing       @relation(fields: [listingId], references: [id], onDelete: Cascade)
  user      User          @relation(fields: [userId], references: [id], onDelete: Cascade)

  @@unique([listingId, userId]) // One interest per user per listing
  @@map("listing_interests")
}



// =========================
// FINANCIAL METRICS MODEL
// =========================
model ListingFinancialMetric {
  id                      String  @id @default(uuid())
  listingId               String  @unique
  ebitda                  String?
  revenueYoYChange        String?
  ebitdaYoYChange         String?
  profitMarginYoYChange   String?
  createdAt               DateTime @default(now())
  updatedAt               DateTime @updatedAt

  // Relations
  listing                 Listing @relation(fields: [listingId], references: [id], onDelete: Cascade)

  @@map("listing_financial_metrics")
}

// =========================
// MONTHLY FINANCIAL MODEL
// =========================
model MonthlyFinancial {
  id           String   @id @default(uuid())
  listingId    String
  month        Int      // 1-12
  year         Int
  revenue      String
  ebitda       String?
  profitMargin String?
  createdAt    DateTime @default(now())
  updatedAt    DateTime @updatedAt

  // Relations
  listing      Listing  @relation(fields: [listingId], references: [id], onDelete: Cascade)

  @@unique([listingId, month, year]) // One record per listing per month/year
  @@map("monthly_financials")
}

model Admin {
  id        String   @id @default(uuid())
  name      String
  email     String   @unique
  password  String
  image     String?
  phone     String?
  country   String?
  address   String?
  location  String?
  company   String?
  website   String?
  bio       String?
  createdAt DateTime @default(now())
  updatedAt DateTime @updatedAt
  isDeleted Boolean  @default(false)
  role      String   @default("ADMIN")

  @@map("admins")
}

model PasswordReset {
  id        String   @id @default(uuid())
  email     String
  otp       String
  expiresAt DateTime
  createdAt DateTime @default(now())

  @@map("password_resets")
}

model TempUser {
  id        String     @id @default(uuid())
  name      String
  email     String     @unique
  phone     String?
  password  String
  createdAt DateTime   @default(now())
  isDeleted Boolean    @default(false)
  status    UserStatus @default(PENDING)
  updatedAt DateTime   @updatedAt
  roles     UserRole[] @default([BUSINESS_OWNER])

  @@map("temp_users")
}

enum ListingStatus {
  PENDING
  APPROVED
  REJECTED
}

enum InterestStatus {
  PENDING   // when investor clicks "interested"
  APPROVED  // when business owner accepts
  REJECTED  // when business owner rejects
}

enum UserRole {
  BUSINESS_OWNER
  INVESTOR
  CONSULTANT
}

enum UserStatus {
  ACTIVE
  REJECTED
  PENDING
}

enum ListingCategory {
  ECOMMERCE
  MOBILE
  FINTECH
}

enum BusinessType {
  SMALL_BUSINESS
  STARTUP
  ENTERPRISE
}
