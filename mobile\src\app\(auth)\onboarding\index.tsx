import React from 'react'
import OnboardingStep1 from '@/components/auth/OnboardingStep1'
import { useOnboardingGuard } from '@/hooks/useNavigationGuard'
import { GuardedScreen } from '@/components/guards/GuardedScreen'

export default function OnboardingScreen() {
  // Navigation guard - prevent access if not logged in or already completed
  const { canAccess, isLoading } = useOnboardingGuard()

  return (
    <GuardedScreen
      isLoading={isLoading}
      canAccess={canAccess}
      loadingText="Checking onboarding status..."
      accessDeniedText="Redirecting...">
      <OnboardingStep1 />
    </GuardedScreen>
  )
}
