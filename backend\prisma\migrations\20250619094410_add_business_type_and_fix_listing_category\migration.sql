/*
  Warnings:

  - You are about to drop the `_ListingToCategory` table. If the table is not empty, all the data it contains will be lost.
  - You are about to drop the `listing_categories` table. If the table is not empty, all the data it contains will be lost.

*/
-- CreateEnum
CREATE TYPE "ListingCategory" AS ENUM ('SMALL_BUSINESS', 'ECOMMERCE');

-- CreateEnum
CREATE TYPE "BusinessType" AS ENUM ('SMALL_BUSINESS', 'STARTUP', 'ENTERPRISE');

-- DropForeignKey
ALTER TABLE "_ListingToCategory" DROP CONSTRAINT "_ListingToCategory_A_fkey";

-- DropForeignKey
ALTER TABLE "_ListingToCategory" DROP CONSTRAINT "_ListingToCategory_B_fkey";

-- AlterTable
ALTER TABLE "companies" ADD COLUMN     "businessType" "BusinessType" NOT NULL DEFAULT 'SMALL_BUSINESS';

-- AlterTable
ALTER TABLE "listings" ADD COLUMN     "category" "ListingCategory" NOT NULL DEFAULT 'SMALL_BUSINESS';

-- DropTable
DROP TABLE "_ListingToCategory";

-- DropTable
DROP TABLE "listing_categories";
