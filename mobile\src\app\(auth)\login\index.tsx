import { useState } from 'react'
import {
  View,
  Text,
  TouchableOpacity,
  SafeAreaView,
  ScrollView,
  Alert,
} from 'react-native'
import { useRouter } from 'expo-router'
import Link from 'expo-router/link'
import { Input } from '@/components/auth/Input'
import { Button } from '@/components/auth/Button'
import { HeaderImage } from '@/components/auth/HeaderImage'
import tw from '@/lib/tailwind'
import { colors } from '@/constants/colors'
import { useForm, Controller } from 'react-hook-form'
import { z } from 'zod'
import { zodResolver } from '@hookform/resolvers/zod'
import { authService } from '@/services/auth.service'
import { useLoginGuard } from '@/hooks/useNavigationGuard'
import { GuardedScreen } from '@/components/guards/GuardedScreen'

// Define validation schema
const loginSchema = z.object({
  email: z
    .string()
    .min(1, { message: 'Email is required' })
    .email({ message: 'Invalid email address' }),
  password: z
    .string()
    .min(1, { message: 'Password is required' })
    .min(6, { message: 'Password must be at least 6 characters' }),
})

type LoginFormData = z.infer<typeof loginSchema>

export default function LoginScreen() {
  const router = useRouter()
  const [loading, setLoading] = useState(false)
  const [apiError, setApiError] = useState('')

  // Navigation guard - prevent access if already logged in
  const { canAccess, isLoading: guardLoading } = useLoginGuard()

  const {
    control,
    handleSubmit,
    formState: { errors },
  } = useForm<LoginFormData>({
    resolver: zodResolver(loginSchema),
    defaultValues: {
      email: '',
      password: '',
    },
  })

  const handleLogin = async (data: LoginFormData) => {
    setLoading(true)
    setApiError('')

    try {
      // Call the login API
      await authService.login(data.email, data.password)

      // Navigate to dashboard on success
      router.replace('/(tabs)/dashboard')
    } catch (error) {
      // Handle login errors
      setApiError(error instanceof Error ? error.message : 'Login failed')
      Alert.alert(
        'Login Error',
        error instanceof Error ? error.message : 'Login failed'
      )
    } finally {
      setLoading(false)
    }
  }

  return (
    <GuardedScreen
      isLoading={guardLoading}
      canAccess={canAccess}
      loadingText="Checking login status..."
      accessDeniedText="Already logged in. Redirecting...">
      <SafeAreaView style={tw`flex-1 bg-white`}>
        <ScrollView
          contentContainerStyle={tw`flex-grow pt-4 justify-between px-6`}>
          <View>
            {/* Top section with image */}
            <HeaderImage size="large" />
            {/* Middle section with inputs */}
            <View style={tw`flex-1 justify-center`}>
              <View style={tw`mb-6`}>
                <Text style={tw`text-3xl font-bold text-gray-800 mb-2`}>
                  Welcome
                </Text>
                <Text style={tw`text-gray-600`}>
                  Elevate your career with our all-in-one business guide and
                  networking app.
                </Text>
              </View>
            <Controller
              control={control}
              name="email"
              render={({ field: { onChange, onBlur, value } }) => (
                <Input
                  placeholder="Email"
                  value={value}
                  onChangeText={onChange}
                  onBlur={onBlur}
                  keyboardType="email-address"
                  error={errors.email?.message}
                />
              )}
            />

            <Controller
              control={control}
              name="password"
              render={({ field: { onChange, onBlur, value } }) => (
                <Input
                  placeholder="Password"
                  value={value}
                  onChangeText={onChange}
                  onBlur={onBlur}
                  secureTextEntry
                  error={errors.password?.message}
                />
              )}
            />

            <TouchableOpacity
              style={tw`self-end mb-12`}
              onPress={() => router.push('/forget-pass')}>
              <Text
                style={{ color: colors.primary.DEFAULT, fontWeight: '500' }}>
                Forgot Password?
              </Text>
            </TouchableOpacity>
          </View>
        </View>

        {/* Bottom section with button */}
        <View style={tw`mb-12`}>
          {apiError ? (
            <Text style={tw`text-red-500 mb-2 text-center`}>{apiError}</Text>
          ) : null}
          <Button
            title="Login"
            onPress={handleSubmit(handleLogin)}
            loading={loading}
            variant="primary"
          />

          <View style={tw`flex-row justify-center mt-6`}>
            <Text style={tw`text-gray-600`}>Don't have an account? </Text>
            <Link href="/sign-up" asChild>
              <TouchableOpacity>
                <Text
                  style={{ color: colors.primary.DEFAULT, fontWeight: '500' }}>
                  Sign up here
                </Text>
              </TouchableOpacity>
            </Link>
          </View>
        </View>
      </ScrollView>
    </SafeAreaView>
    </GuardedScreen>
  )
}
