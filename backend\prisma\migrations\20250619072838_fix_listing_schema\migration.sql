/*
  Warnings:

  - You are about to drop the column `category` on the `listings` table. All the data in the column will be lost.

*/
-- AlterTable
ALTER TABLE "listings" DROP COLUMN "category",
ADD COLUMN     "description" TEXT;

-- DropEnum
DROP TYPE "ListingCategory";

-- CreateTable
CREATE TABLE "listing_categories" (
    "id" TEXT NOT NULL,
    "name" TEXT NOT NULL,

    CONSTRAINT "listing_categories_pkey" PRIMARY KEY ("id")
);

-- CreateTable
CREATE TABLE "listing_financial_metrics" (
    "id" TEXT NOT NULL,
    "listingId" TEXT NOT NULL,
    "ebitda" TEXT,
    "revenueYoYChange" TEXT,
    "ebitdaYoYChange" TEXT,
    "profitMarginYoYChange" TEXT,
    "createdAt" TIMESTAMP(3) NOT NULL DEFAULT CURRENT_TIMESTAMP,
    "updatedAt" TIMESTAMP(3) NOT NULL,

    CONSTRAINT "listing_financial_metrics_pkey" PRIMARY KEY ("id")
);

-- CreateTable
CREATE TABLE "monthly_financials" (
    "id" TEXT NOT NULL,
    "listingId" TEXT NOT NULL,
    "month" INTEGER NOT NULL,
    "year" INTEGER NOT NULL,
    "revenue" TEXT NOT NULL,
    "ebitda" TEXT,
    "profitMargin" TEXT,
    "createdAt" TIMESTAMP(3) NOT NULL DEFAULT CURRENT_TIMESTAMP,
    "updatedAt" TIMESTAMP(3) NOT NULL,

    CONSTRAINT "monthly_financials_pkey" PRIMARY KEY ("id")
);

-- CreateTable
CREATE TABLE "_ListingToCategory" (
    "A" TEXT NOT NULL,
    "B" TEXT NOT NULL,

    CONSTRAINT "_ListingToCategory_AB_pkey" PRIMARY KEY ("A","B")
);

-- CreateIndex
CREATE UNIQUE INDEX "listing_categories_name_key" ON "listing_categories"("name");

-- CreateIndex
CREATE UNIQUE INDEX "listing_financial_metrics_listingId_key" ON "listing_financial_metrics"("listingId");

-- CreateIndex
CREATE UNIQUE INDEX "monthly_financials_listingId_month_year_key" ON "monthly_financials"("listingId", "month", "year");

-- CreateIndex
CREATE INDEX "_ListingToCategory_B_index" ON "_ListingToCategory"("B");

-- AddForeignKey
ALTER TABLE "listing_financial_metrics" ADD CONSTRAINT "listing_financial_metrics_listingId_fkey" FOREIGN KEY ("listingId") REFERENCES "listings"("id") ON DELETE CASCADE ON UPDATE CASCADE;

-- AddForeignKey
ALTER TABLE "monthly_financials" ADD CONSTRAINT "monthly_financials_listingId_fkey" FOREIGN KEY ("listingId") REFERENCES "listings"("id") ON DELETE CASCADE ON UPDATE CASCADE;

-- AddForeignKey
ALTER TABLE "_ListingToCategory" ADD CONSTRAINT "_ListingToCategory_A_fkey" FOREIGN KEY ("A") REFERENCES "listings"("id") ON DELETE CASCADE ON UPDATE CASCADE;

-- AddForeignKey
ALTER TABLE "_ListingToCategory" ADD CONSTRAINT "_ListingToCategory_B_fkey" FOREIGN KEY ("B") REFERENCES "listing_categories"("id") ON DELETE CASCADE ON UPDATE CASCADE;
