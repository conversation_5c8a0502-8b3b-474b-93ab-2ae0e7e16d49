import AsyncStorage from '@react-native-async-storage/async-storage'
import { getCurrentUserId } from './auth.service'
import { userService, UserProfile } from './user.service'

export interface NavigationGuardResult {
  canAccess: boolean
  redirectTo?: string
  reason?: string
}

export class NavigationGuard {
  /**
   * Check if user is authenticated
   */
  static async isAuthenticated(): Promise<boolean> {
    try {
      const token = await AsyncStorage.getItem('authToken')
      const userId = await getCurrentUserId()
      return !!(token && userId)
    } catch (error) {
      console.error('Error checking authentication:', error)
      return false
    }
  }

  /**
   * Get user profile data safely
   */
  static async getUserData(): Promise<UserProfile | null> {
    try {
      const isAuth = await this.isAuthenticated()
      if (!isAuth) return null

      const userData = await userService.getUserProfile()
      return userData
    } catch (error) {
      console.error('Error getting user data:', error)
      return null
    }
  }

  /**
   * Check if user has completed profile
   */
  static async hasCompletedProfile(): Promise<boolean> {
    try {
      const userData = await this.getUserData()
      if (!userData) return false

      // Check if user has basic profile information
      return !!(
        userData.name &&
        userData.email &&
        userData.profile?.professionalHeadline &&
        userData.profile?.industrySpecialization
      )
    } catch (error) {
      console.error('Error checking profile completion:', error)
      return false
    }
  }

  /**
   * Check if user has completed company setup
   */
  static async hasCompletedCompany(): Promise<boolean> {
    try {
      const userData = await this.getUserData()
      if (!userData) return false

      // Check if user has company information
      return !!(
        userData.company &&
        userData.company.name &&
        userData.company.industry &&
        userData.company.location
      )
    } catch (error) {
      console.error('Error checking company completion:', error)
      return false
    }
  }

  /**
   * Check if user has completed onboarding (both profile and company)
   */
  static async hasCompletedOnboarding(): Promise<boolean> {
    try {
      const hasProfile = await this.hasCompletedProfile()
      const hasCompany = await this.hasCompletedCompany()
      return hasProfile && hasCompany
    } catch (error) {
      console.error('Error checking onboarding completion:', error)
      return false
    }
  }

  /**
   * Guard for login page - authenticated users should not access
   */
  static async canAccessLogin(): Promise<NavigationGuardResult> {
    const isAuth = await this.isAuthenticated()
    
    if (isAuth) {
      const hasOnboarding = await this.hasCompletedOnboarding()
      return {
        canAccess: false,
        redirectTo: hasOnboarding ? '/(tabs)/(root-layout)/home' : '/onboarding',
        reason: 'User is already logged in'
      }
    }

    return { canAccess: true }
  }

  /**
   * Guard for signup page - authenticated users should not access
   */
  static async canAccessSignup(): Promise<NavigationGuardResult> {
    const isAuth = await this.isAuthenticated()
    
    if (isAuth) {
      const hasOnboarding = await this.hasCompletedOnboarding()
      return {
        canAccess: false,
        redirectTo: hasOnboarding ? '/(tabs)/(root-layout)/home' : '/onboarding',
        reason: 'User is already registered and logged in'
      }
    }

    return { canAccess: true }
  }

  /**
   * Guard for onboarding page - completed users should not access
   */
  static async canAccessOnboarding(): Promise<NavigationGuardResult> {
    const isAuth = await this.isAuthenticated()
    
    if (!isAuth) {
      return {
        canAccess: false,
        redirectTo: '/login',
        reason: 'User must be logged in to access onboarding'
      }
    }

    const hasOnboarding = await this.hasCompletedOnboarding()
    if (hasOnboarding) {
      return {
        canAccess: false,
        redirectTo: '/(tabs)/(root-layout)/home',
        reason: 'User has already completed onboarding'
      }
    }

    return { canAccess: true }
  }

  /**
   * Guard for protected pages - requires authentication and completed onboarding
   */
  static async canAccessProtectedPage(): Promise<NavigationGuardResult> {
    const isAuth = await this.isAuthenticated()
    
    if (!isAuth) {
      return {
        canAccess: false,
        redirectTo: '/login',
        reason: 'User must be logged in'
      }
    }

    const hasOnboarding = await this.hasCompletedOnboarding()
    if (!hasOnboarding) {
      return {
        canAccess: false,
        redirectTo: '/onboarding',
        reason: 'User must complete onboarding first'
      }
    }

    return { canAccess: true }
  }

  /**
   * Get the appropriate redirect route for current user state
   */
  static async getRedirectRoute(): Promise<string> {
    const isAuth = await this.isAuthenticated()
    
    if (!isAuth) {
      return '/login'
    }

    const hasOnboarding = await this.hasCompletedOnboarding()
    if (!hasOnboarding) {
      return '/onboarding'
    }

    return '/(tabs)/(root-layout)/home'
  }
}
